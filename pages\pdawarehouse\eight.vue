<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="标签拆批" @leftclick="leftclick"></uniNavBar>

    <uni-forms
      ref="submitFormRef"
      :modelValue="submitForm"
      label-width="80px"
      label-align="right"
      style="padding: 10rpx"
    >
      <uni-forms-item label="物料标签:" label-width="80px" name="materialLabel">
        <uni-easyinput
          v-model="submitForm.materialLabel"
          :focus="focusProp"
          placeholder="请扫描"
          @confirm="enterBtn"
        />
      </uni-forms-item>
      <uni-forms-item label="料号:" label-width="80px" name="materialCode">
        <uni-easyinput v-model="submitForm.materialCode" disabled />
      </uni-forms-item>
      <uni-forms-item label="批次号:" label-width="80px" name="lotNumber">
        <uni-easyinput v-model="submitForm.lotNumber" disabled />
      </uni-forms-item>
      <uni-forms-item label="数量:" label-width="80px" name="qty">
        <uni-easyinput v-model="submitForm.qty" disabled />
      </uni-forms-item>
      <uni-forms-item label="序列号:" label-width="80px" name="generateSeq">
        <uni-easyinput v-model="submitForm.generateSeq" disabled />
      </uni-forms-item>

      <uni-forms-item
        label="每批数量:"
        label-width="80px"
        :rules="[{ required: true, errorMessage: '请输入' }]"
        required
        name="inNum"
      >
        <uni-number-box
          :step="1"
          :min="1"
          background="#ffffff"
          :max="100000"
          v-model="submitForm.inNum"
        ></uni-number-box>
      </uni-forms-item>

      <uni-forms-item
        label="打印机:"
        name="printer"
        required
        :rules="[{ required: true, errorMessage: '请选择' }]"
      >
        <uni-data-select
          v-model="submitForm.printer"
          :localdata="printList"
        ></uni-data-select>
      </uni-forms-item>
    </uni-forms>

    <van-button type="success" style="margin-left: 30rpx" plain @click="confrimLabel">
      标签打印
    </van-button>
    <van-button type="primary" style="margin: 20rpx" plain @click="submitForm = {}">
      清空
    </van-button>

    <uniTabBar></uniTabBar>
    <HiPrintLabel ref="HiPrintLabelRef" @submit="printerListFn"></HiPrintLabel>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { getMaterialApi, printLabelApi } from "@/request/sys/warehouseApi.js";
import HiPrintLabel from "./modal/HiPrintLabel.vue";

onMounted(() => {
  HiPrintLabelRef.value.getModalFn("PDA2");
});

const leftclick = () => {
  uni.redirectTo({
    url: "/pages/pdawarehouse/index",
  });
};

//获取打印机
let printList = ref([]);
const printerListFn = (val) => {
  printList.value = val.map((item) => {
    item.value = item.name;
    item.text = item.name;
    return item;
  });
};

//标签
let focusProp = ref(true);
let submitFormRef = ref(null);
let submitForm = ref({
  barcode: "",
});
let scanTimer;
let isScanning = false;
const enterBtn = async () => {
  if (!submitForm.value.materialLabel.trim()) {
    return uni.showToast({
      title: "请扫码",
      icon: "none",
    });
  }
  // if (isScanning) return
  // isScanning = true;
  // clearTimeout(scanTimer);
  // scanTimer = setTimeout(() => {
  //   isScanning = false;
  // }, 500);

  if ((submitForm.value.materialLabel.match(/\$/g) || []).length < 3) {
    return uni.showToast({
      title: "扫码格式不正常",
      icon: "none",
    });
  }
  const parts = submitForm.value.materialLabel.split("$");

  submitForm.value.materialCode = parts[0];
  submitForm.value.lotNumber = parts[1];
  submitForm.value.qty = parts[2];
  submitForm.value.generateSeq = parts[3];
  HiPrintLabelRef.value.printerFn();
  // let res = await getMaterialApi({
  //   barcode: submitForm.value.materialLabel
  // })

  // submitForm.value = res
  // submitForm.value.materialCode = res.materialCode
  // submitForm.value.lotNumber = res.lotNumber
  // submitForm.value.qty = res.qty
  // submitForm.value.barcode = res.barcode
  // submitForm.value.seq = res.seq
  // submitForm.value.materialLabel = ""

  focusProp.value = false;
  nextTick(() => {
    focusProp.value = true;
  });
};

let HiPrintLabelRef = ref(null);
//提交
function splitQtyIntoBatches(submitForm) {
    const { materialLabel, materialCode, lotNumber, generateSeq, qty, inNum } = submitForm;
    const arr = [];
    let remainingQty = qty;

    // 计算完整的批次和余数
    const fullBatches = Math.floor(remainingQty / inNum);
    const remainder = remainingQty % inNum;

    // 生成完整的批次（每个批次 qtyStr = inNum）
    for (let i = 0; i < fullBatches; i++) {
        arr.push({
            materialLabel,
            materialCode,
            lotNumber,
            generateSeq:generateSeq+""+i,
            qtyStr: inNum,
        });
    }

    // 如果有余数，添加最后一个批次（qtyStr = 余数）
    if (remainder > 0) {
        arr.push({
            materialLabel,
            materialCode,
            lotNumber,
            generateSeq:generateSeq+""+fullBatches+1,
            qtyStr: remainder,
        });
    }

    return arr;
}
const confrimLabel = async () => {
  let flag = await submitFormRef.value.validate();
  if (flag) {
    // await printLabelApi({
    //   barcode: submitForm.value.barcode,
    //   qty: submitForm.value.inNum,
    // });
    // let obj = {
    //   ...submitForm.value,
    //   materialCode: submitForm.value.materialCode,
    //   lotNumber: submitForm.value.lotNumber,
    //   qty: submitForm.value.qty,
    //   barcode: submitForm.value.barcode,
    //   inNum: submitForm.value.inNum,
    // };
    if(!submitForm.value.inNum ){
      return uni.showToast({
        title: "每批数量不能为0",
        icon: "none",
      });
    }
    if (submitForm.value.inNum > submitForm.value.qty) {
      return uni.showToast({
        title: "每批数量不能大于总数量",
        icon: "none",
      });
    }
    let printArr=splitQtyIntoBatches(submitForm.value)
    console.log(printArr,'打印数据');
    HiPrintLabelRef.value.printFn(printArr, submitForm.value.printer);
    uni.showToast({
      title: "操作成功",
    });
    submitForm.value = {
      materialLabel: "",
    };
  }
};
</script>

<style lang="scss" scoped>
.barcode-btn {
  margin: 100rpx 0;
  width: 150rpx;
}
</style>
