
<script>
import { useUserStore } from '@/store/user';
import { useHomeStore } from '@/store/home.js'
export default {
	onLaunch: function () {
		console.log('App Launch');
		if (uni.getStorageSync('token')) {
			console.log('存在token调用')
			useUserStore().getLoginUserFn()//用户信息
			useUserStore().getDictApiFn()//字典
			useHomeStore().setLineInfoFn()//工单信息
		}
	},
	onShow: function () {
		console.log('App Show');
	},
	onHide: function () {
		console.log('App Hide');
	}
};
</script>

<style lang="scss">
/*每个页面公共css */
::-webkit-scrollbar {
	display: none;
	/* Chrome Safari */
}

:deep(.uni-load-more__text) {
	// height: 80rpx !important;
	// line-height: 80rpx;
	margin-top: -20px;
}

.line1 {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.bottom-btn {
	width: 100%;
	height: 100rpx;
	position: fixed;
	left: 0;
	bottom: 0;
	display: flex;

	.btn {
		width: 50%;
		height: 100%;
		color: white;
		cursor: pointer;
		text-align: center;
		line-height: 100rpx;
		font-size: 50rpx;
	}

	.btn-success {
		background: #4bb555;
	}

	.btn-pramary {
		background: #169bd5;
	}

	.btn-danger {
		background: #f90808;
	}

	.btn-disabled {
		pointer-events: none;
		// opacity: 0.8;
		background: #aaa;
	}
}

.self-check {
	width: 40rpx;
	height: 40rpx;
	background: inherit;
	cursor: pointer;
	background: white;
	box-sizing: border-box;
	border-width: 1px;
	border-style: solid;
	border-color: rgba(170, 170, 170, 1);
	border-radius: 3px;

	&.self-check-disable {
		background-color: rgba(215, 215, 215, 1);
		pointer-events: none;
	}

	// &.self-check-checked {
	// 	background: url('static/finish.png');
	// 	background-size: 30rpx 30rpx;
	// 	background-repeat: no-repeat;
	// 	background-position: center;
	// }
}

.color-success {
	color: #4bb555;
}

.color-danger {
	color: #f90808;
}

.color-pramary {
	color: #169bd5;
}

page {
	background: #f0f2f5;
}

.page-content {
	padding-bottom: 60px;
}
</style>