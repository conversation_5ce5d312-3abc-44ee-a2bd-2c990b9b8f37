<!--
 * @Author: 方志良 
 * @Date: 2025-03-18 14:07:28
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-26 14:39:44
 * @FilePath: \bifang-pda\pages\pdaproduct\three.vue
 * 
-->
<template>
  <div>
    <Unit v-if="type == '单件'"></Unit>
    <Batch v-if="type == '批量'"></Batch>
  </div>
</template>

<script setup>
import {
  ref, onMounted,computed
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import Unit from './BadType/Unit.vue'
import Batch from './BadType/Batch.vue'


let type = computed(() => {
  if (useHomeStore().productObj.optionType != 2 && useHomeStore().productObj.optionType != 4 && useHomeStore().productObj.reportWay == 'BATCH') return '批量'
  return '单件'
})

</script>

<style lang="scss" scoped></style>