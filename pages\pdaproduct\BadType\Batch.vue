<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="不良提报-批量" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">产线信息</van-divider>

    <view class="father-row">
      <view class="son-col" style="flex: 1;"> 产线:{{ useHomeStore().lineObj.lineName }}</view>
      <view class="son-col" style="flex: 1;"> 工位:{{ useHomeStore().lineObj.stationName }}</view>
    </view>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">生产报工</van-divider>

    <van-tabs @click="tabsBtn">
      <van-tab :name="1" title="不良提报" />
      <van-tab :name="2" title="提报记录" />
    </van-tabs>

    <view v-if="activeName == 1" class="tabs-item">
      <van-button type="success" style="margin: 20rpx 0;" @click="detailBtn">工单详情</van-button>

      <uni-forms-item label="产品数量:" label-width="80px" :rules="[{ required: true, errorMessage: '请输入' }]" required
        name="barcode">
        <uni-number-box :step="1" :min="0" background="#ffffff" :max="100000" v-model="submitForm.inNum"></uni-number-box>
      </uni-forms-item>

      <uni-forms-item label="不良描述:" label-width="80px" name="remark">
        <uni-easyinput type="textarea" v-model="submitForm.remark" placeholder="请输入" />
      </uni-forms-item>

      <view style="text-align: center;">
        <van-button type="primary" class="barcode-btn" @click="enterBtn"> 报工 </van-button>
      </view>
    </view>

    <view v-if="activeName == 2">
      <ThreeRecord></ThreeRecord>
    </view>

    <uniTabBar></uniTabBar>

    <OneDialog ref="dialogRef"></OneDialog>
  </view>
</template>

<script setup>
import {
  ref, onMounted
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import OneDialog from '../modal/OneDialog.vue'
import ThreeRecord from '../Record/ThreeRecord.vue'
import { badBatchReportApi } from '@/request/sys/productApi.js'

onMounted(() => { })

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/pdaproduct/index'
  });
}

//tab栏
let activeName = ref(1)
let tabsBtn = (name) => {
  activeName.value = name
}

let submitForm = ref({
})
let isScanning = false
let scanTimer
const enterBtn = async () => {
  if (!submitForm.value.inNum) {
    return uni.showToast({
      title: '请输入数量',
      icon: "none"
    })
  }
  if (isScanning) return
  isScanning = true;
  clearTimeout(scanTimer);
  scanTimer = setTimeout(() => {
    isScanning = false;
  }, 500);

  await badBatchReportApi({
    ...useHomeStore().productObj,
    qty: submitForm.value.inNum,
    badnessDescription: submitForm.value.remark
  })
  uni.showToast({
    title: '操作成功',
  });
  submitForm.value = {

  }
}


//详情
const dialogRef = ref(null)
const detailBtn = () => {
  dialogRef.value.openModal()
}

</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.barcode-btn {
  margin: 100rpx 0;
  width: 150rpx;
}
</style>