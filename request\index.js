/*
 * @Author: 方志良 
 * @Date: 2023-05-15 09:46:36
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-23 14:55:49
 * @FilePath: \bifang-pda\request\index.js
 * 
 */
export const config = {
	baseUrl: "/api"
}
if (process.env.NODE_ENV === 'development') {
	//开发环境
	// #ifdef H5
	// 如需跨域参照以下h5跨域配置
	console.log('这是开发H5的条件')
	config.baseUrl = 'http://*************:8080/api'//刘畅
	// #endif
	// #ifdef APP-PLUS ||MP
	console.log('这个是开发app或者小程序条件')
	config.baseUrl = '/api'
	// #endif
} else {
	//生产环境
	// #ifdef H5
	console.log('这是生产H5的条件')
	config.baseUrl = 'http://************:80/api'
	// #endif

	// #ifdef APP-PLUS ||MP
	console.log('这个是生产app或者小程序条件')
	config.baseUrl = '/api'
	// #endif

}

import {
	encodeURIParams
} from '@/util/strUtil';
export function request(options) {
	{
		return new Promise((resolve, reject) => {

			let token = uni.getStorageSync('token')
			let header = {
				'Content-Type': 'application/json;charset=utf-8',
				'Authorization': 'Bearer ' + token,
			}

			if (options.method === 'get' && options.params) {
				let url = `${options.url}?${encodeURIParams(options.params)}`;
				url = url.slice(0, -1);
				options.params = {};
				options.url = url;
			}
			uni.request({
				url: config.baseUrl + options.url,
				data: options.data || {},
				method: options.method || 'POST',
				header: options.headers || header,
				success: (res) => {

					// 控制台显示数据信息

					const code = res.data.errCode
					//登录过期
					if (code == 20101 || code == 401 || code == 106 || code == 107) {
						uni.showModal({
							title: '系统提示',
							content: '登录状态已过期，您可以继续留在该页面，或者重新登录',
							success: function (res) {
								if (res.confirm) {
									uni.removeStorage({
										key: 'token'
									})
									uni.navigateTo({
										url: '/pages/login/index'
									});
								}
							}
						});
						return
					}
					//40201
					if (code === 500 || code === 9999 || code == 99999 || code == 40201 || code ==
						404) {
						console.error('error')
						uni.showToast({
							title: res.errMessage,
							icon: "none"
						})
						return reject(new Error(res.errMessage))
					}
					if (res.statusCode !== 200) {
						uni.showToast({
							title: res.errMessage,
							icon: "none"
						});
						return reject(new Error(res.errMessage));
					}
					if (res.data.data) {
						resolve(res.data.data)
					} else {
						if (res.data.errMessage) {
							uni.showToast({
								title: res.data.errMessage,
								icon: "none"
							});
							return reject(new Error(res.data.errMessage));
						} else {
							resolve(res.data)
						}
					}
				},
				fail: (err) => {
					// 页面中弹框显示失败
					uni.showToast({
						title: '请求接口失败',
						icon: "none"
					})
					// 返回错误消息
					reject(err)
				},
				catch: (e) => {
					console.error(e);
				}
			})
		})
	}
}


