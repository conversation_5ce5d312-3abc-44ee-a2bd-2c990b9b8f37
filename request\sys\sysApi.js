/*
 * @Author: 方志良 
 * @Date: 2024-11-04 09:23:21
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-26 13:21:46
 * @FilePath: \bifang-pda\request\sys\sysApi.js
 * 
 */
//导入request.js
import {
	request
} from "@/request/index";
// import {
// 	encrypt,
// 	decrypt
// } from '@/util/rsaUtil';//加密解密

// 登录方法
export function login(username, password) {

	// const passwordEncrypt = encrypt(password);//加密密码
	let data = {
		username,
		password,
		type: "PDA"
	}
	return request({
		url: '/login',
		headers: {
			isToken: false,
			'Content-Type': "application/x-www-form-urlencoded"
		},
		method: 'post',
		data: data
	});
}

//个人信息
export function getLoginUserInfo(params) {
	return request({
		url: `/userInfo/${params.id}/PDA/getInfo`,
		method: 'get',
	});
}

//字典
export function getDictApi() {
	return request({
		url: `/dictData/getAllDict`,
		method: 'get',
	});
}

//菜单路由
export const getRouters = (params) => {
	return request({
		url: `/userInfo/${params.id}/PDA/getRouters`,
		method: 'get'
	});
};

//退出登录
export function logoutApi() {
	return request({
		url: '/logout',
		method: 'post',
	});
}


//修改密码
export function passwordApi() {
	let id = uni.getStorageSync('pdaUserId') || ""
	return request({
		url: `/userInfo/${id}/password`,
		method: 'put',
	});
}

// 获取设备
export const getEquipListApi = (data) =>
	request({
		url: `/deviceInfo/s?page=false`,
		method: 'post',
		data,
	});

// 获取报修设备
export const getBaoXiuEquipListApi = (stationId) =>
	request({
		url: `/stationEquipment/getEquipmentByStation/${stationId}`,
		method: 'get',
	});


// 获取易损备件
export const getPLCSparePageApi = (data) =>
	request({
		url: `/deviceSparePart/s`,
		method: 'post',
		data,
	});


// 获取仓库
export const getWareListApi = () =>
	request({
		url: `/storeInfo/s?page=false`,
		method: 'post'
	});


// 根据工位获取产线信息
export const getStationLineInfoApi = (code) =>
	request({
		url: `/execution/getStationMessageByStationCode/${code}`,
		method: 'get'
	});



