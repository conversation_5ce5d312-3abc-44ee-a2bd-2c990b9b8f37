<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 14:48:08
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-18 11:07:54
 * @FilePath: \bifang-pda\pages\home\mine.vue
 * 
-->
<template>
  <view>
    <view class="back-top">我的</view>
    <view class="back-avator">
      <uni-row style="height: 100%;">
        <uni-col :span="8" class="back-flex">
          <img class="back-img" src="../../static/images/avator.png" alt="头像不存在">
        </uni-col>
        <uni-col :span="16" class="back-flex2">
          <view style="display: flex;">
            <view>姓名:{{ useUserStore().userInfo.name }}</view>
            <view style="margin-left: 50rpx;">岗位:{{ useUserStore().userInfo.postName }}</view>
          </view>
          <view style="display: flex;margin-top: 20rpx;">
            <div class="poster">{{ useUserStore().userInfo.deptName }}</div>
          </view>
        </uni-col>
      </uni-row>
    </view>

    <view class="appback" @click="passwordFn">
      <view> 修改密码 </view>
      <view style="color:#969799;">></view>
    </view>
    <view class="appback">
      <view> APP版本:1.0.0 </view>
      <view style="color:#969799;"></view>
    </view>

    <button class="returnlogin" @click="returnLogin">退出登录</button>

    <uniTabBar></uniTabBar>
    <PasswordDialog ref="passwordRef"></PasswordDialog>
  </view>
</template>

<script setup>
import {
  ref, defineExpose,
} from 'vue';
import { useUserStore } from '@/store/user.js'
import PasswordDialog from './modal/PasswordDialog.vue'
const returnLogin = () => {
  uni.showModal({
    title: '提示',
    content: '确定退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await useUserStore().logoutApiFn()
        uni.navigateTo({
          url: '/pages/login/index'
        });
      }
    }
  });
}

let passwordRef = ref(null)
const passwordFn = () => {
  passwordRef.value.openModal()
}
</script>

<style lang="scss" scoped>
.back-top {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #ffffff;
  font-size: 35rpx;
  background-color: #02a7f0;
  text-align: center;
}

.back-avator {
  margin-top: 2rpx;
  width: 100%;
  height: 200rpx;
  background-image: url('../../static/images/mine.png');
  background-size: 100% 100%;
  border-radius: 5rpx;
}

.back-flex,
.back-flex2 {
  height: 100%;
  display: flex;
  justify-content: center;
}

.back-flex2 {
  flex-direction: column;
  color: #ffffff;
}

.back-flex {
  align-items: center;
}

.back-img {
  width: 100rpx;
  height: 100rpx;
}

.poster {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
  padding: 5rpx;
  border-radius: 10rpx;
  margin-right: 10rpx;
  font-size: 25rpx;
}

.appback {
  margin: 0 40rpx;
  height: 100rpx;
  border-bottom: 1px solid #fafafb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.returnlogin {
  margin: 150rpx 40rpx;
  background-color: #02a7f0;
  color: #ffffff;
}
</style>