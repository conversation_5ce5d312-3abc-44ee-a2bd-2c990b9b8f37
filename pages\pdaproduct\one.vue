<!--
 * @Author: 方志良 
 * @Date: 2025-03-18 14:07:17
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-20 17:08:38
 * @FilePath: \bifang-pda\pages\pdaproduct\one.vue
 * 
-->
<template>
  <div>
    <Unit v-if="type == '单件'"></Unit>
    <Batch v-if="type == '批量'"></Batch>
    <Code v-if="type == '合码'"></Code>
    <Pack v-if="type == '包装'"></Pack>
  </div>
</template>

<script setup>
import {
  ref, onMounted, computed
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import Unit from './ReportType/Unit.vue'
import Batch from './ReportType/Batch.vue'
import Code from './ReportType/Code.vue'
import Pack from './ReportType/Pack.vue'

let type = computed(() => {
  if (useHomeStore().productObj.optionType == 2) return '合码'
  if (useHomeStore().productObj.optionType == 4) return '包装'
  if (useHomeStore().productObj.optionType != 2 && useHomeStore().productObj.optionType != 4 && useHomeStore().productObj.reportWay == 'BATCH') return '批量'
  return '单件'
})

</script>

<style lang="scss" scoped></style>