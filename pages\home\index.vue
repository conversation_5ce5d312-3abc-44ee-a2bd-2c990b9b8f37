<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 09:11:31
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-19 16:58:17
 * @FilePath: \bifang-pda\pages\home\index.vue
 * 
-->
<template>
  <view>
    <view class="back-top">首页</view>

    <uni-row type="flex">
      <uni-col :span="16"  :push="4"  v-for="item in useUserStore().menuList " class="nav-btn" @click="funFn(item)">
        {{ item.meta.title }}
      </uni-col>
    </uni-row>

    <uniTabBar></uniTabBar>
  </view>
</template>

<script setup>
import { useUserStore } from '@/store/user.js'

import {
  ref, onMounted
} from 'vue';

onMounted(() => {
  //获取菜单
  useUserStore().getRoutersFn()
})

//功能
const funFn = (item) => {
  uni.navigateTo({
    url: `/pages${item.path}/index`
  });
}

</script>

<style lang="scss" scoped>
.back-top {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  color: #ffffff;
  font-size: 35rpx;
  background-color: #02a7f0;
  text-align: center;
}
.nav-btn{
  height: 150rpx;
  background-color: #e6f7ff;
  border: 1px solid #f1f2f4;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 50rpx 0;
  font-size: 40rpx;
}
</style>
