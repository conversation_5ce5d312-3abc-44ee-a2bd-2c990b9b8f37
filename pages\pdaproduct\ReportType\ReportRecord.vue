<template>
  <div>
    <uni-search-bar @confirm="searchBtn" cancelButton="always" placeholder="输入关键词搜索" cancelText="搜索" :focus="true"
      v-model="searchValue" @cancel="searchBtn">
    </uni-search-bar>

    <uni-row class="pro-row" v-for="item in recordList" :key="item.id">
      <uni-col :span="24" class="pro-col">
        <view><span>SN条码: </span>{{ item.productSn }}</view>
        <view><span>报工时间: </span>{{ item.reportTime }}</view>
        <view><span>产品名称: </span>{{ item.materialName }}</view>
        <view><span>产品编码: </span>{{ item.materialCode }}</view>
      </uni-col>
      <!-- <uni-col :span="6" class="pro-btn">
          <button size="mini">按钮</button>
        </uni-col> -->
    </uni-row>

  </div>
</template>

<script setup>
import {
  ref, onMounted,
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import { reportRecordApi } from '@/request/sys/productApi.js'

//记录
let recordList = ref([])
const reportRecordApiFn = async () => {
  recordList.value = await reportRecordApi({
    pageIndex: 0,
    pageSize: 100,
    keyword: searchValue.value,
    orderId: useHomeStore().productObj.orderId,
    stationId: useHomeStore().productObj.stationId,
    optionId: useHomeStore().productObj.optionId,
  })
}

onMounted(() => {
  reportRecordApiFn()
})

//搜索框
let searchValue = ref("")
const searchBtn = () => {
  reportRecordApiFn()
}
</script>

<style lang="scss" scoped>
.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }

}
</style>