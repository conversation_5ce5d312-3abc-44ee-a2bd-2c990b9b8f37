/*
 * @Author: 方志良 
 * @Date: 2023-05-15 09:46:37
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-17 10:59:53
 * @FilePath: \bifang-pda\vite.config.js
 * 
 */
import {
	defineConfig,
} from "vite"
import uni from "@dcloudio/vite-plugin-uni";
import Components from 'unplugin-vue-components/vite';
import {
	VantResolver
} from 'unplugin-vue-components/resolvers';

export default defineConfig({
	plugins: [
		uni(),
		Components({
			resolvers: [VantResolver()],
		}),
	],
	server: {
		// port: 5014,
		// host: true,
		// open: false,
		// proxy: {
		// 	'/api': {
		// 		target: "http://*************:8081/api",//刘畅  
		// 		rewrite: p => p.replace(/^\/api/, '')
		// 	}
		// }
	}
})
