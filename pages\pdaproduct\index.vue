<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 09:11:31
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-17 17:46:35
 * @FilePath: \bifang-pda\pages\pdaproduct\index.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="生产作业" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">产线配置</van-divider>

    <view style="display: flex;align-items: center;padding: 15rpx;">
      <span>扫码:</span>
      <uni-easyinput v-model="inputScan" :autofocus="true" placeholder="请扫描" @confirm="enterBtn" />
    </view>
    <view class="father-row">
      <view class="son-col" style="width: 40%;"> 产线:{{ useHomeStore().lineObj.lineName }}</view>
      <view class="son-col" style="width: 40%;;"> 工位:{{ useHomeStore().lineObj.stationName }}</view>
      <view class="son-col" style="width: 40%;;"> 工序:{{ useHomeStore().lineObj.optionName }}</view>
      <!-- <view class="son-col" style="flex: 1;"> <button size="mini" @click="scanBtn">扫描</button></view> -->
    </view>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">功能</van-divider>
    <view class="fun-nav">
      <uni-row>
        <uni-col :span="8" v-for="item in useUserStore().sonMenuList " class="nav-btn" @click="funFn(item)">
          {{ item.meta.title }}
        </uni-col>
      </uni-row>
    </view>
    <uniTabBar></uniTabBar>
  </view>
</template>

<script setup>
import {
  ref, onMounted
} from 'vue';
import { useUserStore } from '@/store/user.js'
import { useHomeStore } from '@/store/home.js'



const leftclick = () => {
  uni.redirectTo({
    url: '/pages/home/<USER>'
  });
}

//功能
const funFn = (item) => {
  console.log(item, useHomeStore().productObj.optionType, 'iiiii')
  if (!useHomeStore().lineObj.stationName) return uni.showToast({
    title: '请扫码后再作业',
    icon: "none"
  })
  // if (item.meta.title == '不良提报' && useHomeStore().productObj.optionType == 2) return uni.showToast({
  //   title: '合码不允许提报不良',
  //   icon: "none"
  // })
  uni.navigateTo({
    url: `/pages/pdaproduct/${item.path}`
  });
}

//按钮扫码
// let barcode = ref("")
// const scanBtn = () => {
//   uni.scanCode({
//     onlyFromCamera: true, // 仅允许从相机扫码
//     scanType: ['barCode', 'qrCode'], // 可扫描条形码和二维码
//     success: (res) => {
//       console.log('扫码结果：', res);
//       barcode.value = res
//       uni.showToast({
//         title: '扫码成功',
//         icon: 'success',
//         duration: 2000
//       });
//     },
//     fail: (err) => {
//       console.log('扫码失败', err);
//     }
//   });
// }


//输入框扫码
let inputScan = ref("")
let enterBtn = () => {
  if (!inputScan.value) return uni.showToast({
    title: '不可为空',
    icon: "none"
  })

  useHomeStore().setLineInfoFn(inputScan.value)
}

onMounted(() => {
  useUserStore().getSonRouterFn('生产作业')
  if (uni.getStorageSync('lineSync') && !useHomeStore().productObj.stationId) {
    useHomeStore().setLineInfoFn()
  }
})

</script>

<style lang="scss" scoped>
.fun-nav {
  .nav-btn {
    height: 180rpx;
    background-color: #e6f7ff;
    border: 1px solid #f1f2f4;
    margin: 20rpx 60rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 40rpx;
  }
}
</style>