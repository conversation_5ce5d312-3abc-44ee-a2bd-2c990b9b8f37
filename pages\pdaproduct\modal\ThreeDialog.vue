<!--
 * @Author: 方志良 
 * @Date: 2024-11-04 10:54:08
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-20 13:13:28
 * @FilePath: \bifang-pda\pages\pdaproduct\modal\ThreeDialog.vue
 * 
-->
<template>
  <uni-popup ref="dialogRef" type="dialog">
    <uni-popup-dialog title="处理" :before-close="true" @close="cancel" @confirm="confirm">
      <uni-forms ref='formRef' :modelValue="dialogForm" style="width: 100%;" :label-width=100>

        <uni-forms-item label="开始时间" required v-if="dialogForm.status == 'REPORT'"
          :rules="[{ required: true, errorMessage: '请输入' }]" name="handleStartTime">
          <uni-datetime-picker v-model="dialogForm.handleStartTime" :clearIcon="false" />
        </uni-forms-item>
        <uni-forms-item label="结束时间" :rules="[{ required: true, errorMessage: '请输入' }]" name="handleEndTime">
          <uni-datetime-picker v-model="dialogForm.handleEndTime" :clearIcon="false" />
        </uni-forms-item>

        <uni-forms-item label="处理内容" name="handleDetail" required :rules="[{ required: true, errorMessage: '请输入' }]">
          <uni-easyinput type="textarea" v-model="dialogForm.handleDetail" placeholder="请输入" />
        </uni-forms-item>

      </uni-forms>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import { handlePageApi } from '@/request/sys/productApi.js'
import {
  ref, defineExpose,
} from 'vue';

const dialogRef = ref(null)
const openModal = (val) => {
  dialogRef.value.open()
  dialogForm.value = val
}
let dialogForm = ref({})

let formRef = ref(null)
const emit = defineEmits(['confirm']);
const confirm = () => {
  console.log(dialogForm.value, 'dialogForm.value')
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      await handlePageApi({
        id: dialogForm.value.id,
        handleStartTime: dialogForm.value.handleStartTime,
        handleEndTime: dialogForm.value.handleEndTime,
        handleDetail: dialogForm.value.handleDetail,
      })

      uni.showToast({ title: '处理成功' });
      emit('confirm');
      cancel()

    }
  })
}
const cancel = () => {
  dialogRef.value.close()
  dialogForm.value = {}
}

defineExpose({
  openModal
});
</script>

<style lang="scss" scoped></style>