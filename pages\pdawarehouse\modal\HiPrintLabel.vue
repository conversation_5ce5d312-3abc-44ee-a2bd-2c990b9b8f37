<template>
  <div id="perview-printTemplate" style="display: none"></div>
</template>

<script setup>
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";
import { onMounted, ref, defineExpose } from "vue";
import { getPrintModalApi } from "@/request/sys/warehouseApi.js";

let hiprintTemplateObj = ref(null);
hiprint.init({
  providers: [defaultElementTypeProvider()],
});
onMounted(() => {});
const emit = defineEmits(["submit"]);

const printDataTemp = ref(null);

//初始化数据
const getModalFn = async (code) => {
  let res = await getPrintModalApi({
    pageIndex: 0,
    pageSize: 10,
    code,
  });
  if (res && res.length > 0) {
    let printData = JSON.parse(res[0].printData);
    printDataTemp.value = printData;
    hiprintInitFn({
      panels: [printData],
    });
  }
};

//初始化
const hiprintInitFn = (template) => {
  hiprintTemplateObj.value = new hiprint.PrintTemplate({
    template,
  });
  hiprintTemplateObj.value.design("#perview-printTemplate");

  if (process.env.NODE_ENV === "development") {
    hiprint.init({
      host: "http://************:17522", // 此处输入服务启动后的生产地址
      token: "hiprint", // 用于鉴权的token，hiprint* （*可替换为[0-9a-zA-Z\-_]字符）
    });
  } else {
    hiprint.init({
      host: "http://************:17522", // 此处输入服务启动后的生产地址
      token: "hiprint", // 用于鉴权的token，hiprint* （*可替换为[0-9a-zA-Z\-_]字符）
    });
  }
};

//获取打印机列表
const printerFn = () => {
  let printer = hiprint.hiwebSocket.printerList;
  emit("submit", printer);
};

const printFn = (printArr, printerName) => {
  //浏览器打印
  // hiprintTemplateObj.value.print(printArr, { leftOffset: -1, topOffset: -1 }, {
  //   callback: () => { console.log("浏览器打印窗口已打开"); },
  //   styleHandler: () => "<style>.hiprint-printElement-text{color:red !important;}</style>"
  // });

  // 直接打印
  let clientId;
  let client = hiprint.hiwebSocket.clients;
  for (let c in client) {
    const i = client[c];
    if (i) {
      clientId = i.clientId;
      break; // 如果只要一个打印客户端 直接默认取第一个就行了
    }
  }

  //设置常量值！
  for (let i = 0; i < printDataTemp.value.printElements.length; i++) {
    let ele = printDataTemp.value.printElements[i];
    console.log(2222, ele.options);
    if (ele.options && ele.options.data && ele.options.field) {
      if (Array.isArray(printArr)) {
        for (let i = 0; i < printArr.length; i++) {
          printArr[i][ele.options.field] = ele.options.data;
        }
      } else {
        printArr[ele.options.field] = ele.options.data;
      }
    }
  }
  hiprintTemplateObj.value.print2(
    printArr,
    {
      client: clientId,
      printer: printerName, // 测试使用第4个打印机
      title: "hiprint测试打印",
    },
    {
      callback: () => {
        nextTick(() => {
          console.log("打印成功");
        });
      },
    }
  );
};

defineExpose({
  printFn, //打印
  printerFn, //打印机列表
  getModalFn, //初始化
});
</script>

<style lang="scss" scoped></style>
