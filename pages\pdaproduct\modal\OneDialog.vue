<!--
 * @Author: 方志良 
 * @Date: 2024-11-04 10:54:08
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-26 14:53:17
 * @FilePath: \bifang-pda\pages\pdaproduct\modal\OneDialog.vue
 * 
-->
<template>
  <uni-popup ref="dialogRef" type="dialog">
    <uni-popup-dialog title="工单详情" :before-close="true" @close="cancel" cancelText="关闭" :isOk="false">
      <uni-row class="pro-row">
        <uni-col :span="24" class="pro-col">
          <view><span>ERP工单号: </span>{{ useHomeStore().productObj.erpOrderNumber }}</view>
          <view><span>MES工单号: </span>{{ useHomeStore().productObj.mesOrderNumber }}</view>
          <view><span>产品名称: </span>{{  useHomeStore().productObj.materialName}}</view>
          <view><span>产品编码: </span>{{ useHomeStore().productObj.materialCode }}</view>
          <view><span>规格型号: </span>{{ useHomeStore().productObj.materialSpecification }}</view>
          <view><span>产品版本: </span>{{ useHomeStore().productObj.visiable }}</view>
          <view><span>数量: </span>{{ useHomeStore().productObj.qty }}</view>
          <view><span>产出数量: </span>{{ useHomeStore().productObj.outQty }}</view>
          <view><span>不良数量: </span>{{ useHomeStore().productObj.badnessQty }}</view>
        </uni-col>
      </uni-row>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import {
  ref, defineExpose,
} from 'vue';
import { useHomeStore } from '@/store/home.js'

const dialogRef = ref(null)
const openModal = () => {
  dialogRef.value.open()
}
const emit = defineEmits(['confirm']);

const cancel = () => {
  dialogRef.value.close()
}

defineExpose({
  openModal
});
</script>

<style lang="scss" scoped>
.pro-col {
  view {
    margin-bottom: 25rpx;
  }

  span {
    color: #666666;
  }
}
</style>