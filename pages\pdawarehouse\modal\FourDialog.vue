<template>
  <uni-popup ref="dialogRef" type="dialog">
    <uni-popup-dialog title="添加" :before-close="true" @close="cancel" @confirm="confirm">
      <uni-forms ref='formRef' :modelValue="dialogForm" style="width: 100%;" :label-width=80>


        <uni-forms-item label="来源仓库:" name="sourceWarehouse" required :rules="[{ required: true, errorMessage: '请选择' }]">
          <uni-data-select v-model="dialogForm.sourceWarehouse" :localdata="wareList"></uni-data-select>
        </uni-forms-item>

        <uni-forms-item label="目标仓库:" name="targetWarehouse" required :rules="[{ required: true, errorMessage: '请选择' }]">
          <uni-data-select v-model="dialogForm.targetWarehouse" :localdata="wareList"></uni-data-select>
        </uni-forms-item>

        <uni-forms-item label="备注:" name="remark" required :rules="[{ required: true, errorMessage: '请输入' }]">
          <uni-easyinput type="textarea" v-model="dialogForm.remark" placeholder="请输入" />
        </uni-forms-item>

      </uni-forms>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import { addChangeSparPageApi } from '@/request/sys/productApi.js'
import { getWareListApi } from '@/request/sys/sysApi.js'
import {
  ref, defineExpose,
} from 'vue';


//仓库
let wareList = ref([])
let getWareListApiFn = async () => {
  let res = await getWareListApi({
    status: 'ENABLE',
  })
  wareList.value = res.map(item => {
    item.value = item.code
    item.text = item.name
    return item
  })
}

const dialogRef = ref(null)
const openModal = () => {
  dialogRef.value.open()
  getWareListApiFn()
}
let dialogForm = ref({})

let formRef = ref(null)
const emit = defineEmits(['confirm']);
const confirm = () => {
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      await addChangeSparPageApi({
        repairTaskId: dialogForm.value.id,
        sparePartId: dialogForm.value.useNum,
        useNum: dialogForm.value.useNum
      })

      uni.showToast({ title: '操作成功' });
      emit('confirm');
      cancel()
    }
  })
}
const cancel = () => {
  dialogRef.value.close()
  dialogForm.value = {}
}

defineExpose({
  openModal
});
</script>

<style lang="scss" scoped></style>