<template>
  <view class="uni-stat__select">
    <span v-if="label" class="uni-label-text hide-on-phone">{{ label + "：" }}</span>
    <view class="uni-stat-box" :class="{ 'uni-stat__actived': current }">
      <view class="uni-select" :class="{ 'uni-select--disabled': disabled }">
        <view class="uni-select__input-box" @click="toggleSelector">
          <view v-if="current" class="uni-select__input-text">{{ current }}</view>
          <view v-else class="uni-select__input-text uni-select__input-placeholder">{{
            typePlaceholder
          }}</view>
          <uni-icons
            v-if="current && clear && !disabled"
            type="clear"
            color="#c0c4cc"
            size="24"
            @click.stop="clearVal"
          />
          <uni-icons
            v-else
            :type="showSelector ? 'top' : 'bottom'"
            size="14"
            color="#999"
          />
        </view>
        <view class="uni-select--mask" v-if="showSelector" @click="toggleSelector" />
        <view class="uni-select__selector" v-if="showSelector">
          <view class="uni-popper__arrow"></view>
          <!-- 添加搜索框 -->
          <view class="uni-select__search-box" v-if="searchable">
            <uni-icons type="search" size="16" color="#999" />
            <input
              class="uni-select__search-input"
              v-model="searchText"
              :placeholder="searchPlaceholder"
              @input="handleSearch"
              @confirm="handleSearch"
            />
            <uni-icons
              v-if="searchText"
              type="clear"
              size="16"
              color="#999"
              @click="clearSearch"
            />
          </view>
          <scroll-view scroll-y="true" class="uni-select__selector-scroll">
            <view class="uni-select__selector-empty" v-if="filteredData.length === 0">
              <text>{{ emptyTips }}</text>
            </view>
            <view
              v-else
              class="uni-select__selector-item"
              v-for="(item, index) in filteredData"
              :key="index"
              @click="change(item)"
            >
              <text :class="{ 'uni-select__selector__disabled': item.disable }">{{
                formatItemName(item)
              }}</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "uni-stat-select",
  mixins: [uniCloud.mixinDatacom || {}],
  props: {
    // 添加搜索相关props
    searchable: {
      type: Boolean,
      default: true, // 默认开启搜索功能
    },
    searchPlaceholder: {
      type: String,
      default: "搜索",
    },
    filterMethod: {
      type: Function,
      default: null,
    },
    // 原有props保持不变
    localdata: {
      type: Array,
      default() {
        return [];
      },
    },
    value: {
      type: [String, Number],
      default: "",
    },
    modelValue: {
      type: [String, Number],
      default: "",
    },
    label: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    emptyTips: {
      type: String,
      default: "无选项",
    },
    clear: {
      type: Boolean,
      default: true,
    },
    defItem: {
      type: Number,
      default: 0,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 格式化输出 用法 field="_id as value, version as text, uni_platform as label" format="{label} - {text}"
    format: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showSelector: false,
      current: "",
      mixinDatacomResData: [],
      filteredData: [], // 新增过滤后的数据
      searchText: "", // 搜索文本
      apps: [],
      channels: [],
      cacheKey: "uni-data-select-lastSelectedValue",
    };
  },
  created() {
    this.debounceGet = this.debounce(() => {
      this.query();
    }, 300);
    if (this.collection && !this.localdata.length) {
      this.debounceGet();
    }
  },
  computed: {
    typePlaceholder() {
      const text = {
        "opendb-stat-app-versions": "版本",
        "opendb-app-channels": "渠道",
        "opendb-app-list": "应用",
      };
      const common = this.placeholder;
      const placeholder = text[this.collection];
      return placeholder ? common + placeholder : common;
    },
    valueCom() {
      // #ifdef VUE3
      return this.modelValue;
      // #endif
      // #ifndef VUE3
      return this.value;
      // #endif
    },
  },
  watch: {
    localdata: {
      immediate: true,
      handler(val, old) {
        if (Array.isArray(val) && old !== val) {
          this.mixinDatacomResData = val;
          this.filteredData = val;
        }
      },
    },
    valueCom(val, old) {
      this.initDefVal();
    },
    mixinDatacomResData: {
      immediate: true,
      handler(val) {
        if (val.length) {
          this.filteredData = val;
          this.initDefVal();
        }
      },
    },
    // 监听搜索文本变化
    searchText(val) {
      this.handleSearch();
    },
  },
  methods: {
    // 添加搜索处理方法
    handleSearch() {
      if (!this.searchable) return;

      if (!this.searchText) {
        this.filteredData = [...this.mixinDatacomResData];
        return;
      }

      if (this.filterMethod) {
        // 如果有自定义过滤方法，使用自定义方法
        this.filteredData = this.mixinDatacomResData.filter((item) =>
          this.filterMethod(item, this.searchText)
        );
      } else {
        // 默认过滤方法
        const searchLower = this.searchText.toLowerCase();
        this.filteredData = this.mixinDatacomResData.filter((item) => {
          // 尝试获取显示文本
          const displayText = this.formatItemName(item).toLowerCase();
          return displayText.includes(searchLower);
        });
      }
    },

    // 清空搜索
    clearSearch() {
      this.searchText = "";
      this.filteredData = [...this.mixinDatacomResData];
    },

    // 原有方法保持不变
    debounce(fn, time = 100) {
      let timer = null;
      return function (...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, time);
      };
    },
    query() {
      this.mixinDatacomEasyGet();
    },
    onMixinDatacomPropsChange() {
      if (this.collection) {
        this.debounceGet();
      }
    },
    initDefVal() {
      let defValue = "";
      if ((this.valueCom || this.valueCom === 0) && !this.isDisabled(this.valueCom)) {
        defValue = this.valueCom;
      } else {
        let strogeValue;
        if (this.collection) {
          strogeValue = this.getCache();
        }
        if (strogeValue || strogeValue === 0) {
          defValue = strogeValue;
        } else {
          let defItem = "";
          if (this.defItem > 0 && this.defItem <= this.mixinDatacomResData.length) {
            defItem = this.mixinDatacomResData[this.defItem - 1].value;
          }
          defValue = defItem;
        }
        if (defValue || defValue === 0) {
          this.emit(defValue);
        }
      }
      const def = this.mixinDatacomResData.find((item) => item.value === defValue);
      this.current = def ? this.formatItemName(def) : "";
    },

    isDisabled(value) {
      let isDisabled = false;
      this.mixinDatacomResData.forEach((item) => {
        if (item.value === value) {
          isDisabled = item.disable;
        }
      });
      return isDisabled;
    },

    clearVal() {
      this.emit("");
      if (this.collection) {
        this.removeCache();
      }
    },
    change(item) {
      if (!item.disable) {
        this.showSelector = false;
        this.current = this.formatItemName(item);
        this.emit(item.value);
      }
    },
    emit(val) {
      this.$emit("change", val);
      this.$emit("input", val);
      this.$emit("update:modelValue", val);
      if (this.collection) {
        this.setCache(val);
      }
    },
    toggleSelector() {
      if (this.disabled) {
        return;
      }
      this.showSelector = !this.showSelector;
      // 打开下拉框时自动聚焦搜索框
      if (this.showSelector && this.searchable) {
        this.$nextTick(() => {
          // 这里需要根据实际环境获取搜索输入框的引用
          // 如果是H5环境，可以使用DOM API
          // #ifdef H5
          const searchInput = document.querySelector(".uni-select__search-input");
          if (searchInput) {
            searchInput.focus();
          }
          // #endif
        });
      }
    },
    formatItemName(item) {
      let { text, value, channel_code } = item;
      channel_code = channel_code ? `(${channel_code})` : "";

      if (this.format) {
        let str = "";
        str = this.format;
        for (let key in item) {
          str = str.replace(new RegExp(`{${key}}`, "g"), item[key]);
        }
        return str;
      } else {
        return this.collection.indexOf("app-list") > 0
          ? `${text}(${value})`
          : text
          ? text
          : `未命名${channel_code}`;
      }
    },
    getLoadData() {
      return this.mixinDatacomResData;
    },
    getCurrentCacheKey() {
      return this.collection;
    },
    getCache(name = this.getCurrentCacheKey()) {
      let cacheData = uni.getStorageSync(this.cacheKey) || {};
      return cacheData[name];
    },
    setCache(value, name = this.getCurrentCacheKey()) {
      let cacheData = uni.getStorageSync(this.cacheKey) || {};
      cacheData[name] = value;
      uni.setStorageSync(this.cacheKey, cacheData);
    },
    removeCache(name = this.getCurrentCacheKey()) {
      let cacheData = uni.getStorageSync(this.cacheKey) || {};
      delete cacheData[name];
      uni.setStorageSync(this.cacheKey, cacheData);
    },
  },
};
</script>

<style lang="scss">
$uni-base-color: #6a6a6a !default;
$uni-main-color: #333 !default;
$uni-secondary-color: #909399 !default;
$uni-border-3: #e5e5e5;

/* #ifndef APP-NVUE */
@media screen and (max-width: 500px) {
  .hide-on-phone {
    display: none;
  }
}
/* #endif */

.uni-stat__select {
  display: flex;
  align-items: center;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
}

.uni-stat-box {
  width: 100%;
  flex: 1;
}

.uni-stat__actived {
  width: 100%;
  flex: 1;
}

.uni-label-text {
  font-size: 14px;
  font-weight: bold;
  color: $uni-base-color;
  margin: auto 0;
  margin-right: 5px;
}

.uni-select {
  font-size: 14px;
  border: 1px solid $uni-border-3;
  box-sizing: border-box;
  border-radius: 4px;
  padding: 0 5px;
  padding-left: 10px;
  position: relative;
  /* #ifndef APP-NVUE */
  display: flex;
  user-select: none;
  /* #endif */
  flex-direction: row;
  align-items: center;
  border-bottom: solid 1px $uni-border-3;
  width: 100%;
  flex: 1;
  height: 35px;

  &--disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
  }
}

.uni-select__label {
  font-size: 16px;
  height: 35px;
  padding-right: 10px;
  color: $uni-secondary-color;
}

.uni-select__input-box {
  height: 35px;
  position: relative;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex: 1;
  flex-direction: row;
  align-items: center;
}

.uni-select__input {
  flex: 1;
  font-size: 14px;
  height: 22px;
  line-height: 22px;
}

.uni-select__input-plac {
  font-size: 14px;
  color: $uni-secondary-color;
}

.uni-select__selector {
  /* #ifndef APP-NVUE */
  box-sizing: border-box;
  /* #endif */
  position: absolute;
  top: calc(100% + 12px);
  left: 0;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 3;
  padding: 4px 0;
}

/* 搜索框样式 */
.uni-select__search-box {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.uni-select__search-input {
  flex: 1;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin: 0 8px;
  font-size: 14px;
  color: #606266;

  &:focus {
    outline: none;
    border-color: #409eff;
  }
}

.uni-select__selector-scroll {
  /* #ifndef APP-NVUE */
  max-height: 200px;
  box-sizing: border-box;
  /* #endif */
}

.uni-select__selector-empty,
.uni-select__selector-item {
  /* #ifndef APP-NVUE */
  display: flex;
  cursor: pointer;
  /* #endif */
  line-height: 35px;
  font-size: 14px;
  text-align: center;
  padding: 0px 10px;
}

.uni-select__selector-item:hover {
  background-color: #f9f9f9;
}

.uni-select__selector-empty:last-child,
.uni-select__selector-item:last-child {
  /* #ifndef APP-NVUE */
  border-bottom: none;
  /* #endif */
}

.uni-select__selector__disabled {
  opacity: 0.4;
  cursor: default;
}

.uni-popper__arrow,
.uni-popper__arrow::after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
}

.uni-popper__arrow {
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: -6px;
  left: 10%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #ebeef5;
}

.uni-popper__arrow::after {
  content: " ";
  top: 1px;
  margin-left: -6px;
  border-top-width: 0;
  border-bottom-color: #fff;
}

.uni-select__input-text {
  width: 100%;
  color: $uni-main-color;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}

.uni-select__input-placeholder {
  color: $uni-base-color;
  font-size: 12px;
}

.uni-select--mask {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}
</style>
