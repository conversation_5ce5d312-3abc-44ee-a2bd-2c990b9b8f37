import {
  request
} from "@/request/index";

//查找对应的工单信息
export function getProductInfoApi(params) {
  return request({
    url: `/execution/getStationMessage`,
    method: 'get',
    params
  });
}

//生产报工-手动报工-单件
export const unitReportApi = (data) =>
  request({
    url: `/execution/report`,
    method: 'post',
    data
  });

//生产报工-手动报工-批量
export const batchReportApi = (data) =>
  request({
    url: `/execution/reportBatch`,
    method: 'post',
    data
  });

//生产报工-手动报工-合码
export const codeReportApi = (data) =>
  request({
    url: `/execution/reportH`,
    method: 'post',
    data
  });

//生产报工-手动报工-包装
export const packReportApi = (data) =>
  request({
    url: `/execution/reportBox`,
    method: 'post',
    data
  });

//生产报工-报工记录
export const reportRecordApi = (data) =>
  request({
    url: `/prodProductReportRecord/getListPage`,
    method: 'post',
    data
  });

// 获取异常代码
export const getCodePageApi = (data) =>
  request({
    url: `/qualityExceptionCode/s?page=false`,
    method: 'post',
    data
  });

// 新增异常类型
export const addCodePageApi = data =>
  request({
    // url: `/qualityExceptionType`,
    url:"/qualityProductionExceptionRecord",
    method: 'post',
    data
  });

// 获取生产异常记录
export const getAbandonPageApi = (data) =>
  request({
    url: `/qualityProductionExceptionRecord/s`,
    method: 'post',
    data
  });

//处理
export const handlePageApi = data =>
  request({
    url: `/qualityProductionExceptionRecord/startHandle`,
    method: 'post',
    data
  });

//处理完成
export const successHandlePageApi = data =>
  request({
    url: `/qualityProductionExceptionRecord/handleEnd`,
    method: 'post',
    data
  });


//生产报工-不良提报-单件
export const badReportApi = (data) =>
  request({
    url: `/execution/badness`,
    method: 'post',
    data
  });

//生产报工-不良提报-批量
export const badBatchReportApi = (data) =>
  request({
    url: `/execution/badnessBatch`,
    method: 'post',
    data
  });

//生产报工-物料上线-扫码上线
export const scanUpMaterialApi = (data) =>
  request({
    url: `/execution/materialOnlineByBarcode`,
    method: 'post',
    data
  });


// 获取维修任务
export const getMaintainPageApi = (data) =>
  request({
    url: `/deviceRepairTask/s`,
    method: 'post',
    data,
  });

//单个
export const getUnitMaintainPageApi = (id) =>
  request({
    url: `/deviceRepairTask/${id}`,
    method: 'get',
  });

// 新增维修任务
export const addMaintainPageApi = data =>
  request({
    url: `/deviceRepairTask`,
    method: 'post',
    data
  });

// 获取更换备件
export const getChangeSparePageApi = (data,) =>
  request({
    url: `/deviceSparePartChangeRecord/s`,
    method: 'post',
    data
  });

// 新增更换备件
export const addChangeSparPageApi = data =>
  request({
    url: `/deviceSparePartChangeRecord`,
    method: 'post',
    data
  });

// 删除更换备件
export const delChangeSparPageApi = data =>
  request({
    url: `/deviceSparePartChangeRecord/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });

// 保存维修任务
export const saveMaintainPageApi = data =>
  request({
    url: `/deviceRepairTask/saveRepairTask`,
    method: 'post',
    data
  });


// 完成维修任务
export const successMaintainPageApi = data =>
  request({
    url: `/deviceRepairTask/completeRepairTask`,
    method: 'post',
    data
  });

//生产报工-物料上线-上线页面
export const upMaterialPageApi = (data) =>
  request({
    url: `/prodOnlineMaterial/getListPage`,
    method: 'post',
    data
  });

//生产报工-不良记录
export const getBadReportPageApi = (data) =>
  request({
    url: `/prodBadness/getListPage`,
    method: 'post',
    data
  });




