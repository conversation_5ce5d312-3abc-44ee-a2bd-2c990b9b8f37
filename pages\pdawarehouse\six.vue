<!--
 * @Author: 方志良 
 * @Date: 2025-03-18 14:09:30
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-11 09:34:15
 * @FilePath: \bifang-pda\pages\pdawarehouse\six.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="客退单作业" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
      客退单选择
    </van-divider>

    <view style="padding: 10rpx">
      <!-- <button type="primary" plain size="mini" style="margin-bottom: 10rpx;" @click="addOrderBtn">建立客退单</button> -->
      <uni-forms-item
        label="客退单"
        required
        name="guestReturnId"
        :rules="[{ required: true, errorMessage: '请选择' }]"
        style="width: 100%"
      >
        <uni-data-select
          v-model="submitForm.guestReturnId"
          :localdata="requestList"
        ></uni-data-select>
      </uni-forms-item>
    </view>

    <view v-if="submitForm.guestReturnId">
      <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
        扫码作业
      </van-divider>

      <van-tabs @click="tabsBtn">
        <van-tab :name="1" title="扫码" />
        <van-tab :name="2" title="客退单详情" />
      </van-tabs>

      <view v-if="activeName == 1" class="tabs-item">
        <uni-forms-item
          label="产品条码:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="barcode"
        >
          <uni-easyinput
            v-model="submitForm.barcode"
            :focus="focusProp"
            placeholder="请扫描"
            @confirm="enterBtn"
          />
        </uni-forms-item>

        <view style="text-align: center">
          <van-button type="primary" class="bar-btn" @click="enterBtn">提交</van-button>
        </view>

        <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
          单据作业
        </van-divider>

        <view style="text-align: center">
          <van-button type="primary" class="bar-btn" @click="successBtn">
            出库完成
          </van-button>
        </view>
      </view>

      <view v-if="activeName == 2">
        <uni-search-bar
          @confirm="searchBtn"
          cancelButton="always"
          placeholder="输入关键词搜索"
          cancelText="搜索"
          :focus="true"
          v-model="searchValue"
          @cancel="searchBtn"
        ></uni-search-bar>

        <uni-row class="pro-row" v-for="item in recordList" :key="item.id">
          <uni-col :span="18" class="pro-col">
            <view>
              <span>批次号:</span>
              {{ item.lotNumber }}
            </view>
            <view>
              <span>SN条码:</span>
              {{ item.barcode }}
            </view>
            <view>
              <span>物料名称:</span>
              {{ item.materialName }}
            </view>
            <view>
              <span>物料编码:</span>
              {{ item.materialCode }}
            </view>
            <view>
              <span>物料数量:</span>
              {{ item.qty }}
            </view>
          </uni-col>
          <uni-col :span="6" class="pro-btn">
            <button size="mini" type="warn" @click="deleteBtn(item)">删除</button>
          </uni-col>
        </uni-row>
      </view>
    </view>
    <uniTabBar></uniTabBar>
    <FiveDialog ref="dialogRef" @confirm="getGuestReturnPageApiFn"></FiveDialog>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import {
  getGuestReturnPageApi,
  submitGuestReturnPageApi,
  getDetaiGuestReturnPageApi,
  successGuestReturnPageApi,
  delGuestReturApi,
} from "@/request/sys/warehouseApi.js";
import FiveDialog from "./modal/FiveDialog.vue";

//客退单
let requestList = ref([]);
let getGuestReturnPageApiFn = async () => {
  let res = await getGuestReturnPageApi({
    orderBy: "createTime",
    orderDirection: "DESC",
  });
  requestList.value = res.map((item) => {
    item.value = item.id;
    item.text = item.returnNumber;
    return item;
  });
};
onMounted(() => {
  getGuestReturnPageApiFn();
});

//新建
let dialogRef = ref(null);
const addOrderBtn = () => {
  dialogRef.value.openModal();
};

const leftclick = () => {
  uni.redirectTo({
    url: "/pages/pdawarehouse/index",
  });
};

//tab栏
let activeName = ref(1);
let tabsBtn = (name) => {
  activeName.value = name;
  if (name == 2) {
    getDetaiGuestReturnPageApiFn();
  }
};

//提交
let submitForm = ref({
  barcode: "",
});
let focusProp = ref(true);
const enterBtn = async () => {
  if (!submitForm.value.barcode.trim())
    return uni.showToast({
      title: "不可为空",
      icon: "none",
    });

  try {
    await submitGuestReturnPageApi({
      id: submitForm.value.guestReturnId,
      barcode: submitForm.value.barcode,
    });
    uni.showToast({
      title: "操作成功",
    });
  } finally {
    focusProp.value = false;
    submitForm.value.barcode = "";
    nextTick(() => {
      focusProp.value = true;
    });
  }
};

//出库完成
const successBtn = () => {
  uni.showModal({
    title: "提示",
    content: "确定出库完成吗？",
    success: async (res) => {
      if (res.confirm) {
        await successGuestReturnPageApi({ id: submitForm.value.guestReturnId });
        uni.showToast({ title: "出库成功" });
        submitForm.value = {};
      }
    },
  });
};

//搜索框
let searchValue = ref("");
const searchBtn = () => {
  getDetaiGuestReturnPageApiFn();
};

//记录
let recordList = ref([]);
const getDetaiGuestReturnPageApiFn = async () => {
  recordList.value = await getDetaiGuestReturnPageApi({
    orCfgs: [
      {
        name: "outStorageNumber",
        type: "like:B",
        value: "",
      },
    ],
    pageIndex: 0,
    pageSize: 100,
    keyword: searchValue.value,
    returnId: submitForm.value.guestReturnId,
  });
};

//删除记录
const deleteBtn = (row) => {
  uni.showModal({
    title: "提示",
    content: "确定删除吗？",
    success: async (res) => {
      if (res.confirm) {
        await delGuestReturApi(row.id);
        uni.showToast({ title: "删除成功" });
        getDetaiGuestReturnPageApiFn();
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.bar-btn {
  margin: 40rpx 0;
  width: 200rpx;
}

.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }
}

.form-row {
  border: 1px solid #d9d9d9;
  padding: 10rpx;
  font-size: 20rpx;
}
</style>
