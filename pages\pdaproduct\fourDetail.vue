<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="故障报修详情" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">报修信息</van-divider>
    <uni-row class="pro-row">
      <uni-col :span="24" class="pro-col">
        <view><span>任务单号: </span>{{ rowObj.taskCode }}</view>
        <view><span>设备编号: </span>{{ rowObj.deviceName }}</view>
        <view><span>提报时间: </span>{{ rowObj.reportTime }}</view>
        <view><span>异常详情: </span>{{ rowObj.exceptionDetail }}</view>
      </uni-col>
    </uni-row>

    <van-button type="primary" style="margin: 20rpx;" @click="saveBtn" v-if="status != 'REPAIR_END'"> 保存 </van-button>
    <van-button type="primary" style="margin: 20rpx;" @click="successBtn" v-if="status != 'REPAIR_END'"> 完成维修
    </van-button>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">维修内容</van-divider>

    <uni-easyinput type="textarea" v-model="repairDetail" :disabled="status == 'REPAIR_END'" placeholder="请输入维修内容" />


    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">备品更换记录</van-divider>


    <view style="text-align: center;">
      <van-button type="primary" size="mini" style="margin-bottom: 20rpx;" v-if="status != 'REPAIR_END'" plain
        @click="addBtn"> 添加备品更换记录 </van-button>
    </view>

    <uni-row class="pro-row" v-for="item in spareList" :key="item.id">
      <uni-col :span="18" class="pro-col">
        <view><span>备件名称: </span>{{ item.name }}</view>
        <view><span>备件编码: </span>{{ item.code }}</view>
        <view><span>使用数量: </span>{{ item.useNum }}</view>
        <view><span>使用寿命: </span>{{ item.usefulLife }}</view>
      </uni-col>
      <uni-col :span="6" class="pro-btn">
        <button size="mini" type="warn" @click="deleteFn(item)" v-if="status != 'REPAIR_END'">删除</button>
      </uni-col>
    </uni-row>


    <uniTabBar></uniTabBar>

    <FourDialog ref="dialogThreeRef" @confirm="getChangeSparePageApiFn"></FourDialog>
  </view>
</template>

<script setup>
import {
  ref, onMounted
} from 'vue';
import { onLoad } from "@dcloudio/uni-app"
import { useHomeStore } from '@/store/home.js'
import FourDialog from './modal/FourDialog.vue'
import { getChangeSparePageApi, getUnitMaintainPageApi, saveMaintainPageApi, successMaintainPageApi, delChangeSparPageApi } from '@/request/sys/productApi.js'
import { getEquipListApi } from '@/request/sys/sysApi.js'

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/pdaproduct/four'
  });
}

let rowObj = ref({})
let status = ref("")
onLoad(async (options) => {
  if (options.id) {
    rowObj.value = await getUnitMaintainPageApi(options.id)
    getChangeSparePageApiFn()
    repairDetail.value = rowObj.value.repairDetail
  }
  status.value = options.status
})

//异常代码
let equipList = ref([])

let getEquipListApiFn = async () => {
  let res = await getEquipListApi({ status: 'ENABLE' })
  equipList.value = res.map(item => {
    item.value = item.id
    item.text = item.name
    return item
  })
}

//新增
let dialogThreeRef = ref(null)
const addBtn = async () => {
  dialogThreeRef.value.openModal(rowObj.value)
}

//提交
let repairDetail = ref("")

//备件记录
let spareList = ref([])
const getChangeSparePageApiFn = async () => {
  spareList.value = await getChangeSparePageApi({
    repairTaskId: rowObj.value.id,
    pageIndex: 0,
    pageSize: 100,
  })
}
//删除
const deleteFn = (row) => {
  uni.showModal({
    title: '提示',
    content: '确定删除吗？',
    success: async (res) => {
      if (res.confirm) {
        await delChangeSparPageApi({ id: row.id })
        uni.showToast({ title: '删除成功' });
        getChangeSparePageApiFn()
      }
    }
  });
}

//保存
const saveBtn = async () => {
  if (!repairDetail.value.trim()) return uni.showToast({
    title: '请输入维修内容',
    icon: "none"
  })
  await saveMaintainPageApi({
    id: rowObj.value.id,
    repairDetail: repairDetail.value
  })
  uni.showToast({ title: '保存成功' });
  uni.redirectTo({
    url: '/pages/pdaproduct/four'
  });
}

//完成
const successBtn = async () => {
  if (!repairDetail.value.trim()) return uni.showToast({
    title: '请输入维修内容',
    icon: "none"
  })
  await successMaintainPageApi({
    id: rowObj.value.id,
    repairDetail: repairDetail.value
  })
  uni.showToast({ title: '保存成功' });
  uni.redirectTo({
    url: '/pages/pdaproduct/four'
  });
}

onMounted(() => {
  getEquipListApiFn()
})
</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}


.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }

}
</style>