<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 09:11:31
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-26 14:36:21
 * @FilePath: \bifang-pda\pages\pdaproduct\two.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="物料上线" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">产线信息</van-divider>

    <view class="father-row">
      <view class="son-col" style="flex: 1;"> 产线:{{ useHomeStore().lineObj.lineName }}</view>
      <view class="son-col" style="flex: 1;"> 工位:{{ useHomeStore().lineObj.stationName }}</view>
    </view>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">生产报工</van-divider>

    <van-tabs @click="tabsBtn">
      <van-tab :name="1" title="物料上线" />
      <van-tab :name="2" title="上线记录" />
    </van-tabs>

    <view v-if="activeName == 1" class="tabs-item">
      <van-button type="success" style="margin: 20rpx 0;" @click="detailBtn">工单详情</van-button>

      <uni-forms-item label="物料标签:" label-width="80px" :rules="[{ required: true, errorMessage: '请输入' }]" required
        name="barcode">
        <uni-easyinput v-model="submitForm.barcode" :focus="focusProp" placeholder="请扫描" @confirm="enterBtn" />
      </uni-forms-item>
      <view style="text-align: center;">
        <van-button type="primary" class="barcode-btn" @click="enterBtn"> 上线 </van-button>
      </view>
    </view>

    <view v-if="activeName == 2">
      <TwoRecord></TwoRecord>
    </view>

    <uniTabBar></uniTabBar>

    <OneDialog ref="dialogRef"></OneDialog>
  </view>
</template>

<script setup>
import {
  ref, onMounted, nextTick
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import OneDialog from './modal/OneDialog.vue'
import TwoRecord from './Record/TwoRecord.vue'
import { scanUpMaterialApi } from '@/request/sys/productApi.js'

onMounted(() => { })

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/pdaproduct/index'
  });
}

//tab栏
let activeName = ref(1)
let tabsBtn = (name) => {
  activeName.value = name
}

let submitForm = ref({
  barcode: ""
})
let focusProp = ref(true)
let isScanning = false
let scanTimer
const enterBtn = async () => {
  if (!submitForm.value.barcode.trim()) {
    return uni.showToast({
      title: '请扫码',
      icon: "none"
    })
  }
  if (isScanning) return
  isScanning = true;
  clearTimeout(scanTimer);
  scanTimer = setTimeout(() => {
    isScanning = false;
  }, 500);

  await scanUpMaterialApi({
    ...useHomeStore().productObj,
    barcode: submitForm.value.barcode
  })
  focusProp.value = false
  uni.showToast({
    title: '操作成功',
  });
  submitForm.value = {
    barcode: ""
  }
  nextTick(() => {
    focusProp.value = true
  })
}


//详情
const dialogRef = ref(null)
const detailBtn = () => {
  dialogRef.value.openModal()
}

</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.barcode-btn {
  margin: 100rpx 0;
  width: 150rpx;
}
</style>