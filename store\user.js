/*
 * @Author: 方志良 
 * @Date: 2024-06-11 09:05:49
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-26 13:56:13
 * @FilePath: \bifang-pda\store\user.js
 * 
 */
import { defineStore } from 'pinia'
import { getLoginUserInfo, logout<PERSON>pi, getRouters, getDict<PERSON><PERSON> } from '../request/sys/sysApi'

export const useUserStore = defineStore(
  'user-info',
  {
    state: () => ({
      userInfo: {},//个人信息
      permissions: [],//按钮权限
      menuList: [],//菜单
      sonMenuList: [],//当前子菜单
      dictList: [],//字典
    }),
    actions: {
      //登录信息
      async getLoginUserFn() {
        let res = await getLoginUserInfo({
          id: uni.getStorageSync('pdaUserId') || ""
        })
        this.userInfo = res.personnel || {}
        this.permissions = res.permissions || []
      },

      //菜单
      async getRoutersFn() {
        this.menuList = await getRouters({
          id: uni.getStorageSync('pdaUserId') || ""
        })
      },

      //子菜单
      async getSonRouterFn(title) {
        let updateFn = () => {
          let obj = this.menuList.find(item => item.meta.title == title)
          if (obj) {
            this.sonMenuList = obj.children.filter(item => !item.hidden)
          } else {
            this.sonMenuList = []
          }
        }
        if (title) {
          if (this.menuList.length > 0) {
            updateFn()
          } else {
            this.menuList = await getRouters({
              id: uni.getStorageSync('pdaUserId') || ""
            })
            updateFn()
          }
        } else {
          console.error('请传父组件的标题')
        }
      },

      //字典
      async getDictApiFn() {
        this.dictList = await getDictApi()
      },

      //退出登录
      logoutApiFn() {
        return new Promise(async (resolve, reject) => {
          try {
            await logoutApi()
            this.userInfo = {}
            this.dictTypes = []
            uni.removeStorageSync('token')
            resolve();
          } catch (error) {
            reject(error)
          }
        })
      }
    }
  },
  {
    persist: true
  }
)

