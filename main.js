/*
 * @Author: 方志良 
 * @Date: 2023-05-15 09:46:36
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-11-05 08:54:09
 * @FilePath: \pda\main.js
 * 
 */
import App from './App'
import './src/styles/page.scss'; // global css

// #ifndef VUE3
console.log('不等于vue3')
import Vue from 'vue'

Vue.config.productionTip = false
App.mpType = 'app'

import { createApp } from 'vue'
import Vant from 'vant'
import 'vant/lib/index.css'
//持久化存储插件
import persist from 'pinia-plugin-persistedstate'

const aPP = createApp(App)

aPP.use(Vant)

aPP.mount('#app')

aPP.use(createPinia().use(persist))

try {
	function isPromise(obj) {
		return (
			!!obj &&
			(typeof obj === "object" || typeof obj === "function") &&
			typeof obj.then === "function"
		);
	}

	// 统一 vue2 API Promise 化返回格式与 vue3 保持一致
	uni.addInterceptor({
		returnValue(res) {
			if (!isPromise(res)) {
				return res;
			}
			return new Promise((resolve, reject) => {
				res.then((res) => {
					if (res[0]) {
						reject(res[0]);
					} else {
						resolve(res[1]);
					}
				});
			});
		},
	});
} catch (error) { }

const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp,
} from 'vue'
import * as pinia from 'pinia'
import hasPermi from './util/permission'


export function createApp() {
	const app = createSSRApp(App)
	app.use(pinia.createPinia())
	app.directive('hasPermi', hasPermi);
	return {
		app,
		pinia
	}
}
// #endif