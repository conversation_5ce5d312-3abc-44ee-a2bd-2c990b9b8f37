<!--
 * @Author: 方志良 
 * @Date: 2025-03-22 13:51:40
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-22 13:32:21
 * @FilePath: \bifang-pda\pages\pdawarehouse\modal\SelectPrinter.vue
 * 
-->
<template>
  <view>
    <uni-popup ref="dialogRef" type="dialog">
      <uni-popup-dialog
        title="批量打印"
        :before-close="false"
        @close="cancel"
        @confirm="confirm"
        confirmText="确定"
      >
        <uni-forms
          ref="formRef"
          :modelValue="dialogForm"
          style="width: 100%"
          :label-width="5"
        >
          <uni-forms-item
            label=""
            name="printer"
            required
            :rules="[{ required: true, errorMessage: '请选择' }]"
          >
            <uni-data-select
              v-model="dialogForm.printer"
              :localdata="printList"
            ></uni-data-select>
          </uni-forms-item>
        </uni-forms>
      </uni-popup-dialog>
    </uni-popup>
    <HiPrintLabel ref="HiPrintLabelRef" @submit="printerListFn"></HiPrintLabel>
  </view>
</template>

<script setup>
import HiPrintLabel from "./HiPrintLabel.vue";
import { ref, defineExpose, onMounted } from "vue";

let HiPrintLabelRef = ref(null);
onMounted(() => {
  HiPrintLabelRef.value.getModalFn("BoxRecord");
});

//获取打印机
let printList = ref([]);
const printerListFn = (val) => {
  printList.value = val.map((item) => {
    item.value = item.name;
    item.text = item.name;
    return item;
  });
};

const dialogRef = ref(null);
const openModal = () => {
  dialogRef.value.open();
  HiPrintLabelRef.value.printerFn();
};
let dialogForm = ref({});

let formRef = ref(null);
const emit = defineEmits(["confirm"]);
const confirm = () => {
  console.log(dialogForm.value.printer, "dialogForm.value.printer");
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      uni.showToast({ title: "打印机选择成功" });
      uni.setStorageSync("printerName", dialogForm.value.printer);
      emit("confirm", dialogForm.value.printer);
      cancel();
    }
  });
};
const cancel = () => {
  console.log(dialogRef.value,'dialogRef.value')
  if (dialogRef.value) {
    dialogRef.value.close();
  }
  dialogForm.value = {};
};
const printFn = (printArr) => {
  if (!uni.getStorageSync("printerName"))
    return uni.showToast({ title: "请先选择打印机", icon: "none" });
  HiPrintLabelRef.value.printFn(printArr, uni.getStorageSync("printerName"));
};

defineExpose({
  openModal,
  printFn,
});
</script>

<style lang="scss" scoped></style>
