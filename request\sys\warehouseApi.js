/*
 * @Author: 方志良 
 * @Date: 2025-03-22 11:20:19
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-25 17:15:47
 * @FilePath: \bifang-pda\request\sys\warehouseApi.js
 * 
 */
import {
  request
} from "@/request/index";

/**
 * 领料单作业
 */

// 获取产线列表
export const getLineListApi = (data) =>
  request({
    url: `/line/s?page=false`,
    method: 'post',
    data
  });

// 领料单列表
export const getRequisitionNumberLstApi = (params) =>
  request({
    url: `/storeRequisition/getListNotFinish?page=false`,
    method: 'get',
    params
  });

// 领料单物料新增
export const addMaterialPageApi = (data) =>
  request({
    url: `/storeRequisition/pdaAddInfo`,
    method: 'post',
    data,
  });


// 领料单物料分页
export const getMaterialPageApi = (data) =>
  request({
    url: `/storeRequisitionInfo/getList`,
    method: 'post',
    data
  });

// 领料单删除
export const delMaterialPageApi = (id) =>
  request({
    url: `/storeRequisitionInfo/${id}?batch=false`,
    method: 'delete',
  });

/**
 * 退料单
 */

//ERP工单
export const getERPorderListApi = (data) =>
  request({
    url: `/prodOrder/s?page=false`,
    method: 'post',
    data
  });

//建立退料单
export const addReturnPageApi = (data) =>
  request({
    url: `/storeReturnMaterial`,
    method: 'post',
    data,
  });

//选择退料单
export const getReturnPageApi = (params) =>
  request({
    url: `/storeReturnMaterial/getListNotFinish?page=false`,
    method: 'get',
    params
  });

//退料单扫码提交
export const submitReturnPageApi = (data) =>
  request({
    url: `/storeReturnMaterial/pdaScanBarcode`,
    method: 'post',
    data,
  });

//退料单完成
export const successReturnPageApi = (data) =>
  request({
    url: `/storeReturnMaterial/returnStoreDirect`,
    method: 'post',
    data,
  });

//退料单详情
export const getDetailReturnPageApi = (data) =>
  request({
    url: `/storeReturnMaterial/getReturnMaterialInfo`,
    method: 'post',
    data,
  });

//退料单删除
export const delReturnPageApi = (id) =>
  request({
    url: `/storeReturnMaterialItem/${id}?batch=false`,
    method: 'delete',
  });


/**
* 入库单
*/
//建立入库单
export const addInWarePageApi = (data) =>
  request({
    url: `/storeInStorage`,
    method: 'post',
    data,
  });

//选择入库单
export const getInWarePageApi = (params) =>
  request({
    url: `/storeInStorage/getListNotFinish?page=false`,
    method: 'get',
    params
  });

//入库单扫码提交
export const submitInWarePageApi = (data) =>
  request({
    url: `/storeInStorage/pdaScanBarcode`,
    method: 'post',
    data,
  });

//入库单完成
export const successInWarePageApi = (data) =>
  request({
    url: `/storeInStorage/inStorageDirect`,
    method: 'post',
    data,
  });

//入库单详情
export const getDetailInWarePageApi = (data) =>
  request({
    url: `/storeInStorage/getInStorageInfo`,
    method: 'post',
    data,
  });

//入库单删除
export const delInWarePageApi = (id) =>
  request({
    url: `/storeInStorageItem/${id}?batch=false`,
    method: 'delete',
  });


/**
* 调拨单
*/
//选择调拨单
export const getDispatchPageApi = (params) =>
  request({
    url: `/storeMove/getListNotFinish?page=false`,
    method: 'get',
    params
  });

//调拨单扫码提交
export const submitDispatchPageApi = (data) =>
  request({
    url: `/storeMove/pdaAddInfo`,
    method: 'post',
    data,
  });

//调拨单完成
export const successDispatchPageApi = (data) =>
  request({
    url: `/storeMove/finish`,
    method: 'post',
    data,
  });

//调拨单详情
export const getDetailDispatchPageApi = (data) =>
  request({
    url: `/storeMoveInfo/getList`,
    method: 'post',
    data,
  });

//调拨单删除
export const delDetailDispatchPagepi = (id) =>
  request({
    url: `/storeMoveInfo/${id}?batch=false`,
    method: 'delete',
  });

/**
* 出库单
*/
//选择出库单
export const getOutwarePageApi = (params) =>
  request({
    url: `/storeOutStorage/getListNotFinish?page=false`,
    method: 'get',
    params
  });

//虚拟选择出库单
export const getVirtualOutwarePageApi = (params) =>
  request({
    url: `/storeOutStorage/getListNotFinishVirtual?page=false`,
    method: 'get',
    params
  });

  //虚拟选择发货单
export const getVirtualShipPageApi = (data) =>
  request({
    url: `/storeSaleDelivery/s`,
    method: 'post',
    data
  });


//出库单扫码提交
export const submitOutwarePageApi = (data) =>
  request({
    url: `/storeOutStorage/pdaAddInfo`,
    method: 'post',
    data,
  });

//虚拟出库单扫码提交
export const submitVirtualOutwarePageApi = (data) =>
  request({
    url: `/storeOutStorage/pdaAddInfo1`,
    method: 'post',
    data,
  });

  //特殊出库单扫码提交
export const submitSpecialOutwarePageApi = (data) =>
  request({
    url: `/storeOutStorage/pdaAddInfoNew`,
    method: 'post',
    data,
  });


//出库单完成
export const successOutwarePageApi = (data) =>
  request({
    url: `/storeOutStorage/finish`,
    method: 'post',
    data,
  });

//出库单详情
export const getDetailOutwarePageApi = (data) =>
  request({
    url: `/storeOutStorageInfo/getList`,
    method: 'post',
    data,
  });

//销售出库单删除
export const deOutwarePagepi = (id) =>
  request({
    url: `/storeOutStorageInfo/${id}?batch=false`,
    method: 'delete',
  });

/**
* 客退单
*/
//选择客退单
export const getGuestReturnPageApi = (params) =>
  request({
    url: `/storeCustomerReturn/getListNotFinish?page=false`,
    method: 'get',
    params
  });

//客退单扫码提交
export const submitGuestReturnPageApi = (data) =>
  request({
    url: `/storeCustomerReturn/pdaAddInfo`,
    method: 'post',
    data,
  });

//客退单完成
export const successGuestReturnPageApi = (data) =>
  request({
    url: `/storeCustomerReturn/finish`,
    method: 'post',
    data,
  });

//客退单详情
export const getDetaiGuestReturnPageApi = (data) =>
  request({
    url: `/storeCustomerReturnInfo/getList`,
    method: 'post',
    data,
  });


//客退单删除
export const delGuestReturApi = (id) =>
  request({
    url: `/storeCustomerReturnInfo/${id}?batch=false`,
    method: 'delete',
  });



/**
* 拆合箱
*/
//拆箱
export const setSplitBoxApi = (data) =>
  request({
    url: `/prodBox/splitBox`,
    method: 'post',
    data,
  });

//有箱码
export const setYesSplitBoxApi = (data) =>
  request({
    url: `/prodBox/addBoxIsHasBox`,
    method: 'post',
    data,
  });

//扫码箱码获取详情
export const getBoxInfoApi = (params) =>
  request({
    url: `/prodBox/getProdBoxByBoxSn`,
    method: 'get',
    params,
  });

//无箱码
export const setNoSplitBoxApi = (data) =>
  request({
    url: `/prodBox/addBoxIsNoBox`,
    method: 'post',
    data,
  });


/**
* 标签拆批
*/
//获取物料详情
export const getMaterialApi = (data) =>
  request({
    url: `/prodWarehouse/getBarcodeMessage`,
    method: 'post',
    data
  });

//提交
export const printLabelApi = (data) =>
  request({
    url: `/prodWarehouse/subBarcode`,
    method: 'post',
    data,
  });

//根据编号查询模板
export const getPrintModalApi = (data) =>
  request({
    url: `/printTemplate/s`,
    method: 'post',
    data,
  });





