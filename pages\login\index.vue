<template>
	<div class="login">
		<view class="logo"> </view>

		<view class="title-con">
			<text class="title2">毕方MES系统</text>
			<text class="title1">-PDA应用-</text>
		</view>

		<view class="login-form">
			<uni-forms ref='loginRef' class="form-con" :modelValue="loginForm" :rules="rules" :label-width=70>
				<uni-forms-item label="账号" required name="username">
					<uni-easyinput type="text" v-model="loginForm.username" placeholder="用户名" />
				</uni-forms-item>
				<uni-forms-item label="密码" required name="password">
					<uni-easyinput type="password" v-model="loginForm.password" placeholder="密码" />
				</uni-forms-item>
			</uni-forms>
		</view>
		<view class='login-checkbox'>
			<uni-data-checkbox multiple v-model="mindpassword" :localdata="range">
			</uni-data-checkbox>
		</view>

		<view class="flex-view">
			<van-button type="primary" :loading="loadings" class="button" @click="submit">登录</van-button>
		</view>
	</div>
</template>

<script setup>
import {
	ref,
	onMounted
} from 'vue';
import * as syspi from '@/request/sys/sysApi.js';
import {
	onShow
} from '@dcloudio/uni-app';
import { useUserStore } from '@/store/user.js'

const rules = ref({
	username: {
		rules: [{
			required: true,
			errorMessage: '请输入用户名',
		}]
	},
	password: {
		rules: [{
			required: true,
			errorMessage: '请输入密码',
		}]
	}
})
const loginForm = ref({
	username: uni.getStorageSync('usernames') || '',
	password: uni.getStorageSync('passwords') || ''
});
const mindpassword = ref(uni.getStorageSync('checkFlag') ? [1] : [])
const range = ref([{
	'value': 1,
	'text': '记住账号密码'
}])
const loginRef = ref(null)
const loadings = ref(false)

onMounted(() => {

})

onShow(() => {
	let tokens = uni.getStorageSync('token')
	if (tokens) {
		uni.switchTab({
			url: '/pages/home/<USER>'
		});
	}
})

function submit() {
	loginRef.value.validate().then((valid) => {
		if (valid) {
			loadings.value = true
			if (mindpassword.value && mindpassword.value.length > 0) {
				uni.setStorageSync('usernames', loginForm.value.username)
				uni.setStorageSync('passwords', loginForm.value.password)
				uni.setStorageSync('checkFlag', JSON.stringify(mindpassword.value))
			} else {
				uni.removeStorageSync('usernames')
				uni.removeStorageSync('passwords')
				uni.setStorageSync('checkFlag')
			}

			syspi.login(loginForm.value.username, loginForm.value.password).then(res => {
				uni.showToast({
					title: '登录成功',
				});
				uni.setStorageSync('token', res.Authorization)//
				uni.setStorageSync('pdaUserId', res.id)
				uni.navigateTo({
					url: '/pages/home/<USER>'
				});
				useUserStore().getLoginUserFn()//用户信息
				useUserStore().getDictApiFn()//字典
			}).finally(() => {
				loadings.value = false
			})
		}
	})
}

function loadStorage() {
	loginForm.value.username = uni.getStorageSync('usernames') || ''
	loginForm.value.password = uni.getStorageSync('passwords') || ''
}

loadStorage()
</script>

<style lang="scss" scoped>
.login {
	width: 100%;
	height: 100vh;
	background-image: url('../../static/images/background.png');
	background-repeat: no-repeat;
}

.logo {
	width: 100%;
	height: 250rpx;
	margin: 0 auto;
	padding-top: 60rpx;
	background-image: url('../../static/images/logo.png');
	background-repeat: no-repeat;
	overflow: hidden;
	background-size: 500rpx 120rpx;
	background-position: 120rpx 150rpx;
}

.input-icon {
	width: 20px;
	height: 20px;
}

:deep(.is-input-border) {
	flex-direction: row-reverse;
	padding: 0 20rpx;
}

:deep(.content-clear-icon) {
	position: absolute;
}

.title-con {
	padding-top: 50rpx;
	text-align: center;
	color: #333333;
	display: flex;
	flex-direction: column;
	align-items: center;
	font-weight: 650;

	.title1 {
		font-size: 40rpx;
		margin-bottom: 20rpx;
		// color: #1989fa;
	}

	.title2 {
		font-size: 60rpx;
		// color: #1989fa;
	}
}

.login-form {

	width: 100%;
	height: 18vh;
	padding-top: 1vh;
	margin-top: 1vh;
	position: relative;

	.form-con {
		width: 90%;
		margin: 0 5%;
	}
}

.login-checkbox {
	margin: 20rpx 40rpx;
}

:deep(.checklist-text span) {
	color: black;
}

.bottom {
	position: fixed;
	font-size: 26rpx;
	color: #7f7f7f;
	bottom: 20rpx;
	text-align: center;
	width: 100vw;
}
</style>
