<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="产品拆/合箱" @leftclick="leftclick"></uniNavBar>

    <van-tabs @click="tabsBtn">
      <van-tab :name="1" title="拆箱" />
      <van-tab :name="2" title="合箱(有箱码)" />
      <van-tab :name="3" title="合箱(无箱码)" />
    </van-tabs>

      <view>
        <van-button
          type="primary"
          v-if="activeName == 1"
          style="margin-left: 20rpx"
          @click="submitBtn1"
        >
          拆箱
        </van-button>
        <van-button
          type="primary"
          v-if="activeName == 2"
          style="margin-left: 20rpx"
          @click="submitBtn2"
        >
          合箱
        </van-button>
        <van-button
          type="primary"
          v-if="activeName == 3"
          style="margin-left: 30rpx"
          @click="enterBtn3"
        >
          完成
        </van-button>
      </view>
      <van-button type="success" style="margin: 20rpx" plain @click="printBtn">
        选择打印机
      </van-button>

      打印机:{{ printerName }}

    <view v-if="activeName == 1" class="tabs-item">
      <uni-forms-item label="产品条码:" label-width="80px" name="productCode">
        <uni-easyinput
          v-model="productCode"
          :focus="focusProp"
          placeholder="请扫描"
          @confirm="enterBtn1"
        />
      </uni-forms-item>

      <uni-row class="form-row" v-for="(item, index) in scan2List" :key="item">
        <uni-col :span="20">
          {{ item }}
        </uni-col>
        <uni-col :span="4">
          <span @click="delete2CodeBtn(index)" style="color: red">删除</span>
        </uni-col>
      </uni-row>
      <!-- <view style="text-align: center">
        
      </view> -->
    </view>

    <view v-if="activeName == 2" class="tabs-item">
      <uni-forms
        ref="productHeRef"
        :modelValue="productHeForm"
        label-width="80px"
        label-align="right"
        style="padding: 10rpx"
      >
        <uni-forms-item
          label="箱标签:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="boxLabel"
        >
          <uni-easyinput
            v-model="productHeForm.boxLabel"
            ref="focus2Ref"
            placeholder="请扫描"
            @confirm="enterBtn2"
          />
        </uni-forms-item>
        <view style="margin-left: 40rpx">数量:{{ productHeForm.productNum }}</view>
        <uni-forms-item
          label="产品条码:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="productSN"
        >
          <uni-easyinput
            v-model="productHeForm.productSN"
            ref="focusRef"
            placeholder="请扫描"
            @confirm="enterInput"
          />
        </uni-forms-item>
      </uni-forms>

      <uni-row class="form-row" v-for="(item, index) in scan3List" :key="item">
        <uni-col :span="20">
          {{ item }}
        </uni-col>
        <uni-col :span="4">
          <span @click="delete3CodeBtn(index)" style="color: red">删除</span>
        </uni-col>
      </uni-row>
    </view>

    <view v-if="activeName == 3" class="tabs-item">
      <uni-forms
        ref="submitFormRef"
        :modelValue="submitForm"
        label-width="80px"
        label-align="right"
        style="padding: 10rpx"
      >
        <uni-forms-item
          label="装箱数量:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="inNum"
        >
          <uni-number-box
            :step="1"
            :min="1"
            background="#ffffff"
            :max="100000"
            v-model="submitForm.inNum"
          ></uni-number-box>
        </uni-forms-item>
        <uni-forms-item label="产品条码:" label-width="80px" name="scanSn">
          <uni-easyinput
            v-model="submitForm.scanSn"
            :focus="focusProp"
            placeholder="请扫描"
            @confirm="addCodeBtn"
          />
        </uni-forms-item>
      </uni-forms>
      <uni-row class="form-row" v-for="(item, index) in scanList" :key="item">
        <uni-col :span="20">
          {{ item }}
        </uni-col>
        <uni-col :span="4">
          <span @click="deleteCodeBtn(index)" style="color: red">删除</span>
        </uni-col>
      </uni-row>
    </view>

    <uniTabBar></uniTabBar>
    <SelectPrinter ref="dialogRef" @confirm="printConfirmBtn"></SelectPrinter>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import {
  setSplitBoxApi,
  setYesSplitBoxApi,
  getBoxInfoApi,
  setNoSplitBoxApi,
} from "@/request/sys/warehouseApi.js";
import SelectPrinter from "./modal/SelectPrinter.vue";
onMounted(() => {});

const leftclick = () => {
  uni.redirectTo({
    url: "/pages/pdawarehouse/index",
  });
};

//tab栏
let activeName = ref(1);
let tabsBtn = (name) => {
  activeName.value = name;
  console.log(name, "nanan");
  if (name == 1) {
    focusProp.value = true;
  } else if (name == 2) {
    nextTick(() => {
      focusFn(focus2Ref.value);
    });
  } else if (name == 3) {
    focusFn(focusRef.value);
  }
};

//拆箱
const scan2List = ref([]);
let productCode = ref("");
let isScanning = false;
let scanTimer;
const delete2CodeBtn = (index) => {
  scan2List.value.splice(index, 1);
};
const enterBtn1 = () => {
  if (!productCode.value.trim()) {
    return uni.showToast({
      title: "请扫码",
      icon: "none",
    });
  }
  focusProp.value = false;
  if (!scan2List.value.includes(productCode.value)) {
    scan2List.value.push(productCode.value);
  }
  productCode.value = "";
  nextTick(() => {
    focusProp.value = true;
  });
};

const submitBtn1 = async () => {
  if (!uni.getStorageSync("printerName"))
    return uni.showToast({ title: "请先选择打印机", icon: "none" });
  if (isScanning) return;
  isScanning = true;
  clearTimeout(scanTimer);
  scanTimer = setTimeout(() => {
    isScanning = false;
  }, 500);

  let res = await setSplitBoxApi({
    productSns: scan2List.value,
  });
  console.log(res, "数据");
  dialogRef.value.printFn(res);
  focusProp.value = false;
  uni.showToast({
    title: "操作成功",
  });
  scan2List.value = [];
  nextTick(() => {
    focusProp.value = true;
  });
};

//回车
let blurFn = (blurRef) => {
  if (blurRef) {
    const inputEl = blurRef.$el.querySelector("input, textarea");
    if (inputEl) {
      inputEl.blur();
    }
  }
};
let focusFn = (focusRef) => {
  setTimeout(() => {
    if (focusRef) {
      const inputEl = focusRef.$el.querySelector("input, textarea");
      if (inputEl) {
        inputEl.focus();
      }
    }
  }, 100);
};
let focusRef = ref(null);
let focus2Ref = ref(null);

//合箱
let productHeRef = ref(null);
let productHeForm = ref({});
const enterBtn2 = async () => {
  if (!productHeForm.value.boxLabel.trim()) {
    return uni.showToast({
      title: "请扫码",
      icon: "none",
    });
  }
  let res = await getBoxInfoApi({
    boxSn: productHeForm.value.boxLabel,
  });
  productHeForm.value.productNum = res.qty || 0;
  blurFn(focus2Ref.value);
  focusFn(focusRef.value);
};
const scan3List = ref([]);
const delete3CodeBtn = (index) => {
  scan3List.value.splice(index, 1);
};
const enterInput = () => {
  if (!productHeForm.value.productSN.trim()) {
    return uni.showToast({
      title: "请扫码",
      icon: "none",
    });
  }
  if (scan3List.value.length >= productHeForm.value.productNum) {
    return uni.showToast({
      title: "产品条码已达到指定数量,请点击合箱进行提交",
      icon: "none",
    });
  }
  if (!scan3List.value.includes(productHeForm.value.productSN)) {
    scan3List.value.push(productHeForm.value.productSN);
  }
  productHeForm.value.productSN = "";
  nextTick(() => {
    focusFn(focusRef.value);
  });
};

const submitBtn2 = async () => {
  if (!uni.getStorageSync("printerName"))
    return uni.showToast({ title: "请先选择打印机", icon: "none" });
  let res = await setYesSplitBoxApi({
    boxSn: productHeForm.value.boxLabel,
    productSns: scan3List.value,
  });
  dialogRef.value.printFn(res);
  uni.showToast({
    title: "操作成功",
  });
  scan3List.value = [];
  productHeForm.value = {};
  nextTick(() => {
    focusFn(focus2Ref.value);
  });
};

//第三种
let submitFormRef = ref(null);
let submitForm = ref({
  inNum: 1,
  scanSn: "",
});
const enterBtn3 = async () => {
  if (!uni.getStorageSync("printerName"))
    return uni.showToast({ title: "请先选择打印机", icon: "none" });
  let flag = await submitFormRef.value.validate();
  if (flag) {
    let res = await setNoSplitBoxApi({
      productSns: scanList.value,
    });
    dialogRef.value.printFn(res);
    uni.showToast({
      title: "操作成功",
    });
    scanList.value = [];
    submitForm.value = {
      inNum: 1,
      scanSn: "",
    };
  }
};
let focusProp = ref(true);
let scanList = ref([]);
const addCodeBtn = () => {
  if (!submitForm.value.scanSn.trim()) {
    return uni.showToast({
      title: "请扫码",
      icon: "none",
    });
  }
  if (scanList.value.length >= submitForm.value.inNum) {
    return uni.showToast({
      title: "产品条码已达到装箱数量,请点击完成进行提交",
      icon: "none",
    });
  }
  focusProp.value = false;
  if (!scanList.value.includes(submitForm.value.scanSn)) {
    scanList.value.push(submitForm.value.scanSn);
  }
  submitForm.value.scanSn = "";
  nextTick(() => {
    focusProp.value = true;
  });
};
const deleteCodeBtn = (index) => {
  scanList.value.splice(index, 1);
};
//打印
const printerName = ref(uni.getStorageSync("printerName") || "");
let dialogRef = ref(null);
const printBtn = () => {
  dialogRef.value.openModal();
};
const printConfirmBtn = (printer) => {
  printerName.value = printer;
};
</script>

<style lang="scss" scoped>
.barcode-btn {
  margin: 50rpx 0;
  width: 150rpx;
}

.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }
}

.form-row {
  border: 1px solid #d9d9d9;
  padding: 10rpx;
  font-size: 20rpx;
  margin-bottom: 15rpx;
}
</style>
