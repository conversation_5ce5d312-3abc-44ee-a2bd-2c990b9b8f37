<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="退料单作业" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
      退料单选择
    </van-divider>

    <view style="padding: 10rpx">
      <button
        type="primary"
        plain
        size="mini"
        style="margin-bottom: 10rpx"
        @click="addOrderBtn"
      >
        建立退料单
      </button>
      <uni-forms-item
        label="退料单"
        required
        name="materialId"
        :rules="[{ required: true, errorMessage: '请选择' }]"
        style="width: 100%"
      >
        <uni-data-select
          v-model="submitForm.materialId"
          :localdata="requestList"
        ></uni-data-select>
      </uni-forms-item>
    </view>

    <view v-if="submitForm.materialId">
      <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
        扫码作业
      </van-divider>

      <van-tabs @click="tabsBtn">
        <van-tab :name="1" title="扫码" />
        <van-tab :name="2" title="退料单详情" />
      </van-tabs>

      <view v-if="activeName == 1" class="tabs-item">
        <uni-forms-item
          label="物料条码:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="barcode"
        >
          <uni-easyinput
            v-model="submitForm.barcode"
            :focus="focusProp"
            placeholder="请扫描"
            @confirm="enterBtn"
          />
        </uni-forms-item>

        <view style="text-align: center">
          <van-button type="primary" class="bar-btn" @click="enterBtn">提交</van-button>
        </view>

        <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
          单据作业
        </van-divider>

        <view style="text-align: center">
          <van-button type="primary" class="bar-btn" @click="successBtn">
            入库完成
          </van-button>
        </view>
      </view>

      <view v-if="activeName == 2">
        <uni-search-bar
          @confirm="searchBtn"
          cancelButton="always"
          placeholder="输入关键词搜索"
          cancelText="搜索"
          :focus="true"
          v-model="searchValue"
          @cancel="searchBtn"
        ></uni-search-bar>

        <uni-row class="pro-row" v-for="item in recordList" :key="item.id">
          <uni-col :span="18" class="pro-col">
            <view>
              <span>批次号:</span>
              {{ item.lotNumber }}
            </view>
            <view>
              <span>SN条码:</span>
              {{ item.barcode }}
            </view>
            <view>
              <span>物料名称:</span>
              {{ item.materialName }}
            </view>
            <view>
              <span>物料编码:</span>
              {{ item.materialCode }}
            </view>
            <view>
              <span>物料数量:</span>
              {{ item.qty }}
            </view>
          </uni-col>
          <uni-col :span="6" class="pro-btn">
            <button size="mini" type="warn" @click="deleteBtn(item)">删除</button>
          </uni-col>
        </uni-row>
      </view>
    </view>
    <uniTabBar></uniTabBar>
    <TwoDialog ref="dialogRef" @confirm="getReturnPageApiFn"></TwoDialog>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import {
  getReturnPageApi,
  submitReturnPageApi,
  getDetailReturnPageApi,
  delReturnPageApi,
  successReturnPageApi,
} from "@/request/sys/warehouseApi.js";
import TwoDialog from "./modal/TwoDialog.vue";

//退料单
let requestList = ref([]);
let getReturnPageApiFn = async () => {
  let res = await getReturnPageApi({
    orderBy: "createTime",
    orderDirection: "DESC",
  });
  requestList.value = res.map((item) => {
    item.value = item.id;
    item.text = item.returnNumber;
    return item;
  });
};
onMounted(() => {
  getReturnPageApiFn();
});

//新建
let dialogRef = ref(null);
const addOrderBtn = () => {
  dialogRef.value.openModal();
};

const leftclick = () => {
  uni.redirectTo({
    url: "/pages/pdawarehouse/index",
  });
};

//tab栏
let activeName = ref(1);
let tabsBtn = (name) => {
  activeName.value = name;
  if (name == 2) {
    getDetailReturnPageApiFn();
  }
};

//提交
let submitForm = ref({
  barcode: "",
});
let focusProp = ref(true);
const enterBtn = async () => {
  if (!submitForm.value.barcode.trim())
    return uni.showToast({
      title: "不可为空",
      icon: "none",
    });
  try {
    await submitReturnPageApi({
      id: submitForm.value.materialId,
      barcode: submitForm.value.barcode,
    });
    uni.showToast({
      title: "操作成功",
    });
  } finally {
    focusProp.value = false;
    submitForm.value.barcode = "";
    nextTick(() => {
      focusProp.value = true;
    });
  }
};

//入库完成
const successBtn = () => {
  uni.showModal({
    title: "提示",
    content: "确定入库完成吗？",
    success: async (res) => {
      if (res.confirm) {
        await successReturnPageApi({ id: submitForm.value.materialId });
        uni.showToast({ title: "入库成功" });
        submitForm.value = {};
      }
    },
  });
};

//搜索框
let searchValue = ref("");
const searchBtn = () => {
  getDetailReturnPageApiFn();
};

//记录
let recordList = ref([]);
const getDetailReturnPageApiFn = async () => {
  console.log(
    submitForm.value,
    submitForm.value.materialId,
    "submitForm.value.materialId"
  );
  let res = await getDetailReturnPageApi({
    orCfgs: [
      {
        name: "returnNumber",
        type: "like:B",
        value: "",
      },
    ],
    pageIndex: 0,
    pageSize: 100,
    keyword: searchValue.value,
    id: submitForm.value.materialId,
  });
  recordList.value = res.storeReturnMaterialItems;
};

//删除记录
const deleteBtn = (row) => {
  uni.showModal({
    title: "提示",
    content: "确定删除吗？",
    success: async (res) => {
      if (res.confirm) {
        await delReturnPageApi(row.id);
        uni.showToast({ title: "删除成功" });
        getDetailReturnPageApiFn();
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.bar-btn {
  margin: 40rpx 0;
  width: 200rpx;
}

.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }
}

.form-row {
  border: 1px solid #d9d9d9;
  padding: 10rpx;
  font-size: 20rpx;
}
</style>
