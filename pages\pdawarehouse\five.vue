<!--
 * @Author: 方志良 
 * @Date: 2025-03-18 14:09:30
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-07-07 09:37:56
 * @FilePath: \bifang-pda\pages\pdawarehouse\five.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar
      leftText="返回"
      title="销售出库作业-通用"
      @leftclick="leftclick"
    ></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
      出库单选择
    </van-divider>

    <view style="padding: 10rpx">
      <!-- <button type="primary" plain size="mini" style="margin-bottom: 10rpx;" @click="addOrderBtn">建立出库单</button> -->
      <uni-forms-item
        label="出库单"
        required
        name="outWareId"
        :rules="[{ required: true, errorMessage: '请选择' }]"
        style="width: 100%"
      >
        <uni-data-select
          v-model="submitForm.outWareId"
          :localdata="requestList"
        ></uni-data-select>
      </uni-forms-item>
    </view>

    <view v-if="submitForm.outWareId">
      <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
        扫码作业
      </van-divider>

      <van-tabs @click="tabsBtn">
        <van-tab :name="1" title="扫码" />
        <van-tab :name="2" title="出库单详情" />
      </van-tabs>

      <view v-if="activeName == 1" class="tabs-item">
        <uni-forms-item
          label="产品条码:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="barcode"
        >
          <uni-easyinput
            v-model="submitForm.barcode"
            ref="focusRef"
            placeholder="请扫描"
            @confirm="enterInput"
          />
        </uni-forms-item>

        <uni-forms-item
          label="客户箱码:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="customerBarcode"
        >
          <uni-easyinput
            v-model="submitForm.customerBarcode"
            ref="focus3Ref"
            placeholder="请扫描"
            @confirm="enterFn"
          />
        </uni-forms-item>

        <uni-forms-item
          label="客户箱码:"
          label-width="80px"
          :rules="[{ required: true, errorMessage: '请输入' }]"
          required
          name="customerBarcode2"
        >
          <uni-easyinput
            v-model="submitForm.customerBarcode2"
            ref="focus2Ref"
            placeholder="请再次扫描"
            @confirm="enterBtn"
          />
        </uni-forms-item>

        <view style="text-align: center">
          <van-button type="primary" class="bar-btn" @click="enterBtn">提交</van-button>
        </view>

        <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">
          单据作业
        </van-divider>

        <view style="text-align: center">
          <van-button type="primary" class="bar-btn" @click="successBtn">
            出库完成
          </van-button>
        </view>
      </view>

      <view v-if="activeName == 2">
        <uni-search-bar
          @confirm="searchBtn"
          cancelButton="always"
          placeholder="输入关键词搜索"
          cancelText="搜索"
          :focus="true"
          v-model="searchValue"
          @cancel="searchBtn"
        ></uni-search-bar>

        <uni-row class="pro-row" v-for="item in recordList" :key="item.id">
          <uni-col :span="18" class="pro-col">
            <view>
              <span>批次号:</span>
              {{ item.lotNumber }}
            </view>
            <view>
              <span>SN条码:</span>
              {{ item.barcode }}
            </view>
            <view>
              <span>物料名称:</span>
              {{ item.materialName }}
            </view>
            <view>
              <span>物料编码:</span>
              {{ item.materialCode }}
            </view>
            <view>
              <span>物料数量:</span>
              {{ item.qty }}
            </view>
          </uni-col>
          <uni-col :span="6" class="pro-btn">
            <button size="mini" type="warn" @click="deleteBtn(item)">删除</button>
          </uni-col>
        </uni-row>
      </view>
    </view>
    <uniTabBar></uniTabBar>
    <FiveDialog ref="dialogRef" @confirm="getOutwarePageApiFn"></FiveDialog>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import {
  getOutwarePageApi,
  submitOutwarePageApi,
  getDetailOutwarePageApi,
  successOutwarePageApi,
  deOutwarePagepi,
} from "@/request/sys/warehouseApi.js";
import FiveDialog from "./modal/FiveDialog.vue";

//出库单
let requestList = ref([]);
let getOutwarePageApiFn = async () => {
  let res = await getOutwarePageApi({
    orderBy: "createTime",
    orderDirection: "DESC",
  });
  requestList.value = res.map((item) => {
    item.value = item.id;
    item.text = item.outStorageNumber;
    return item;
  });
};
onMounted(() => {
  getOutwarePageApiFn();
});

//回车
let focusRef = ref(null);
let focus2Ref = ref(null);
let blurFn = (blurRef) => {
  if (blurRef) {
    const inputEl = blurRef.$el.querySelector("input, textarea");
    if (inputEl) {
      inputEl.blur();
    }
  }
};
let focusFn = (focusRef) => {
  setTimeout(() => {
    if (focusRef) {
      const inputEl = focusRef.$el.querySelector("input, textarea");
      if (inputEl) {
        inputEl.focus();
      }
    }
  }, 100);
};
const enterInput = () => {
  blurFn(focusRef.value);
  focusFn(focus3Ref.value);
};
let focus3Ref = ref(null);
const enterFn = () => {
  blurFn(focus3Ref.value);
  focusFn(focus2Ref.value);
};

//新建
let dialogRef = ref(null);
const addOrderBtn = () => {
  dialogRef.value.openModal();
};

const leftclick = () => {
  uni.redirectTo({
    url: "/pages/pdawarehouse/index",
  });
};

//tab栏
let activeName = ref(1);
let tabsBtn = (name) => {
  activeName.value = name;
  if (name == 2) {
    getDetailOutwarePageApiFn();
  }
};

//提交
let submitForm = ref({
  barcode: "",
});
const enterBtn = async () => {
  if (!submitForm.value.barcode)
    return uni.showToast({
      title: "产品条码不可为空",
      icon: "none",
    });
  if (!submitForm.value.customerBarcode || !submitForm.value.customerBarcode2)
    return uni.showToast({
      title: "客户箱码不可为空",
      icon: "none",
    });

  if (submitForm.value.customerBarcode != submitForm.value.customerBarcode2) {
    return uni.showToast({
      title: "两次箱码扫描不一致",
      icon: "none",
    });
  }

  focusFn(focus2Ref.value);
  try {
    await submitOutwarePageApi({
      id: submitForm.value.outWareId,
      barcode: submitForm.value.barcode,
      customerBarcode: submitForm.value.customerBarcode,
    });
    uni.showToast({
      title: "操作成功",
    });
  } finally {
    submitForm.value.barcode = "";
    submitForm.value.customerBarcode = "";
    submitForm.value.customerBarcode2 = "";
    blurFn(focus2Ref.value);
    focusFn(focusRef.value);
  }
};

//出库完成
const successBtn = () => {
  uni.showModal({
    title: "提示",
    content: "确定出库完成吗？",
    success: async (res) => {
      if (res.confirm) {
        await successOutwarePageApi({ id: submitForm.value.outWareId });
        uni.showToast({ title: "出库成功" });
        submitForm.value = {};
      }
    },
  });
};

//搜索框
let searchValue = ref("");
const searchBtn = () => {
  getDetailOutwarePageApiFn();
};

//记录
let recordList = ref([]);
const getDetailOutwarePageApiFn = async () => {
  recordList.value = await getDetailOutwarePageApi({
    orCfgs: [
      {
        name: "outStorageNumber",
        type: "like:B",
        value: "",
      },
    ],
    pageIndex: 0,
    pageSize: 100,
    keyword: searchValue.value,
  outId: submitForm.value.subOrderId,
  });
};

//删除记录
const deleteBtn = (row) => {
  uni.showModal({
    title: "提示",
    content: "确定删除吗？",
    success: async (res) => {
      if (res.confirm) {
        await deOutwarePagepi(row.id);
        uni.showToast({ title: "删除成功" });
        getDetailOutwarePageApiFn();
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.bar-btn {
  margin: 40rpx 0;
  width: 200rpx;
}

.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }
}

.form-row {
  border: 1px solid #d9d9d9;
  padding: 10rpx;
  font-size: 20rpx;
}
</style>
