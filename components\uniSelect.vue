<!--
 * @Author: 方志良 
 * @Date: 2024-06-11 14:42:15
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-06-11 14:56:48
 * @FilePath: \uniapp-pda\components\uniSelect.vue
 * 
-->
<template>
  <div>
    <!-- 选择器弹框 -->
    <van-popup v-model:show="props.isvisable" position="bottom">
      <van-picker :columns="partData" @confirm="partOnConfirm" @cancel="cancelFn" />
    </van-popup>
  </div>
</template>

<script setup>
const props = defineProps({
  isvisable: {
    type: Boolean,
    default: false
  },
  partData: {
    type: Array,
    default: () => []
  },
})

const emit = defineEmits(['update:isvisable', 'confirm']);

const partOnConfirm = (item) => {
  if (item) {
    emit('confirm', item);
    emit('update:isvisable', false);
  }
}

const cancelFn = () => {
  emit('update:isvisable', false);
}

</script>

<style lang="scss" scoped></style>