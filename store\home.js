/*
 * @Author: 方志良 
 * @Date: 2024-06-11 15:01:15
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-17 17:47:18
 * @FilePath: \bifang-pda\store\home.js
 * 
 */
import { defineStore } from 'pinia'
import { getProductInfoApi } from '@/request/sys/productApi.js'
import { getStationLineInfoApi } from '@/request/sys/sysApi.js'

export const useHomeStore = defineStore(
  'home',
  {
    state: () => ({
      lineObj: uni.getStorageSync('lineSync') ? JSON.parse(uni.getStorageSync('lineSync')) : {},//产线信息
      productObj: {},//工单信息
    }),
    actions: {
      //产线-工单信息
      async setLineInfoFn(stationCode) {
        let productFn = (res2) => {
          this.productObj.lineName = res2.lineName
          this.productObj.lineId = res2.lineId
          this.productObj.optionId = res2.optionId
          this.productObj.productEditId = res2.productEditId
          this.productObj.stationId = res2.stationId
          this.productObj.stationName = res2.stationName
          this.productObj.erpOrderNumber = res2.prodOrder.erpOrderNumber
          this.productObj.mesOrderNumber = res2.prodOrder.mesOrderNumber
          this.productObj.materialName = res2.prodOrder.materialName
          this.productObj.materialCode = res2.prodOrder.materialCode
          this.productObj.materialSpecification = res2.prodOrder.materialSpecification
          this.productObj.visiable = res2.prodOrder.visiable
          this.productObj.qty = res2.prodOrder.qty
          this.productObj.outQty = res2.prodOrder.outQty
          this.productObj.badnessQty = res2.prodOrder.badnessQty
          this.productObj.orderId = res2.prodOrder.id
          this.productObj.reportType = res2.prodOrderProcessOption.reportType
          this.productObj.reportWay = res2.prodOrderProcessOption.reportWay//单件批量
          this.productObj.optionType = res2.prodOrderProcessOption.optionType//工序类型
          this.productObj.processId = res2.prodOrderProcessOption.orderProcessId
          this.productObj.optionName = res2.prodOrderProcessOption.optionName //工序
        }
        if (stationCode) {
          let res = await getStationLineInfoApi(stationCode)
          console.log(res, 'ressss')
          let obj = {
            lineId: res.lineId,
            lineName: res.lineName,
            stationCode: stationCode,
            stationName: res.stationName,
            optionName: res.prodOrderProcessOption.optionName
          }
          uni.setStorageSync('lineSync', JSON.stringify(obj));
          this.lineObj = obj
          productFn(res)
        } else {
          let lineObj = uni.getStorageSync('lineSync')
          if (lineObj) {
            let res = await getStationLineInfoApi(JSON.parse(lineObj).stationCode)
            productFn(res)
          }
        }
      },
    }
  }
)
