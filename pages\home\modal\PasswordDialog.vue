<!--
 * @Author: 方志良 
 * @Date: 2025-03-18 10:43:23
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-18 11:01:40
 * @FilePath: \bifang-pda\pages\home\modal\PasswordDialog.vue
 * 
-->
<template>
  <uni-popup ref="dialogRef" type="dialog">
    <uni-popup-dialog title="密码修改" :before-close="true" @close="cancel" @confirm="confirm">
      <uni-forms ref='formRef' :modelValue="dialogForm" :rules="rules" :label-width=50>
        <uni-forms-item label="密码" required name="password">
          <uni-easyinput type="password" v-model="dialogForm.password" placeholder="密码" />
        </uni-forms-item>
      </uni-forms>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import {
  ref, defineExpose,
} from 'vue';
import { passwordApi } from '@/request/sys/sysApi.js'

const dialogRef = ref(null)
const openModal = () => {
  dialogRef.value.open()
}

let dialogForm = ref({
  password: ""
})
const rules = ref({
  password: { rules: [{ required: true, errorMessage: '请输入密码', }] }
})

let formRef = ref(null)
const emit = defineEmits(['confirm']);
const confirm = () => {
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      console.log(passwordApi, '通过')
      await passwordApi()
      cancel()
      uni.showToast({
        title: '修改成功,请重新登录',
      });
      uni.removeStorageSync('token')
      uni.navigateTo({
        url: '/pages/login/index'
      });
    }
  })
}
const cancel = () => {
  dialogRef.value.close()
  dialogForm.value = {}
}

defineExpose({
  openModal
});
</script>

<style lang="scss" scoped></style>