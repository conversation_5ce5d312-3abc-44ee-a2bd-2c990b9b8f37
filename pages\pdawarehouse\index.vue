<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 09:11:31
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-18 14:56:58
 * @FilePath: \bifang-pda\pages\pdawarehouse\index.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="仓库作业" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">功能 </van-divider>
    <view class="fun-nav">
      <uni-row>
        <uni-col :span="8" v-for="item in useUserStore().sonMenuList " class="nav-btn" @click="funFn(item)">
          {{ item.meta.title }}
        </uni-col>
      </uni-row>
    </view>
    <uniTabBar></uniTabBar>
  </view>
</template>

<script setup>
import {
  ref, onMounted
} from 'vue';
import { useUserStore } from '@/store/user.js'

onMounted(() => {
  useUserStore().getSonRouterFn('仓库作业')
})

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/home/<USER>'
  });
}

//功能
const funFn = (item) => {
  uni.navigateTo({
    url: `/pages/pdawarehouse/${item.path}`
  });
}

</script>

<style lang="scss" scoped>
.fun-nav {
  .nav-btn {
    height: 180rpx;
    background-color: #e6f7ff;
    border: 1px solid #f1f2f4;
    margin: 60rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 40rpx;
  }
}
</style>