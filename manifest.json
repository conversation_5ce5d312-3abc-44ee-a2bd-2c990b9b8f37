{"name": "毕方", "appid": "__UNI__FB8C63F", "description": "", "versionName": "1.0.0", "versionCode": 1, "transformPx": false, "app-plus": {"usingComponents": false, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "minSdkVersion": 21}, "ios": {"dSYMs": false}, "sdkConfigs": {"ad": {}, "push": {"unipush": {"version": "2", "offline": true, "fcm": {}}}}, "icons": {"android": {"hdpi": "", "xhdpi": "", "xxhdpi": "", "xxxhdpi": ""}, "ios": {"appstore": "", "ipad": {"app": "", "app@2x": "", "notification": "", "notification@2x": "", "proapp@2x": "", "settings": "", "settings@2x": "", "spotlight": "", "spotlight@2x": ""}, "iphone": {"app@2x": "", "app@3x": "", "notification@2x": "", "notification@3x": "", "settings@2x": "", "settings@3x": "", "spotlight@2x": "", "spotlight@3x": ""}}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"title": "毕方移动端", "router": {"base": "./"}}}