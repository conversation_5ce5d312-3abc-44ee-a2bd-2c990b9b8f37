<!--
 * @Author: 方志良 
 * @Date: 2024-11-04 10:54:08
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-20 18:57:38
 * @FilePath: \bifang-pda\pages\pdaproduct\modal\FourDialog.vue
 * 
-->
<template>
  <uni-popup ref="dialogRef" type="dialog">
    <uni-popup-dialog title="添加" :before-close="true" @close="cancel" @confirm="confirm">
      <uni-forms ref='formRef' :modelValue="dialogForm" style="width: 100%;" :label-width=80>

        <uni-forms-item label="易损备件" required name="sparePartId" :rules="[{ required: true, errorMessage: '请选择' }]"
          style="width: 100%;">
          <uni-data-select v-model="dialogForm.sparePartId" :localdata="equipList"></uni-data-select>
        </uni-forms-item>


        <uni-forms-item label="产品数量:" :rules="[{ required: true, errorMessage: '请输入' }]" required name="useNum">
          <uni-number-box :step="1" :min="0" background="#ffffff" :max="100000"
            v-model="dialogForm.useNum"></uni-number-box>
        </uni-forms-item>

      </uni-forms>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import { getPLCSparePageApi } from '@/request/sys/sysApi.js'
import { addChangeSparPageApi } from '@/request/sys/productApi.js'
import {
  ref, defineExpose,
} from 'vue';

let equipList = ref([])

let getPLCSparePageApiFn = async () => {
  let res = await getPLCSparePageApi({
    deviceId: dialogForm.value.id,
    pageIndex: 0,
    pageSize: 100,
  })
  equipList.value = res.map(item => {
    item.value = item.id
    item.text = item.name
    return item
  })
}

const dialogRef = ref(null)
const openModal = (val) => {
  dialogRef.value.open()
  dialogForm.value = val
  getPLCSparePageApiFn()
}
let dialogForm = ref({})

let formRef = ref(null)
const emit = defineEmits(['confirm']);
const confirm = () => {
  console.log(dialogForm.value, 'dialogForm.value')
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      await addChangeSparPageApi({
        repairTaskId: dialogForm.value.id,
        sparePartId: dialogForm.value.useNum,
        useNum: dialogForm.value.useNum
      })

      uni.showToast({ title: '操作成功' });
      emit('confirm');
      cancel()
    }
  })
}
const cancel = () => {
  dialogRef.value.close()
  dialogForm.value = {}
}

defineExpose({
  openModal
});
</script>

<style lang="scss" scoped></style>