<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 09:11:31
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-26 14:24:33
 * @FilePath: \bifang-pda\pages\pdaproduct\ReportType\Pack.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="生产报工-包装" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">产线信息</van-divider>

    <view class="father-row">
      <view class="son-col" style="flex: 1;"> 产线:{{ useHomeStore().lineObj.lineName }}</view>
      <view class="son-col" style="flex: 1;"> 工位:{{ useHomeStore().lineObj.stationName }}</view>
    </view>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">生产报工</van-divider>

    <van-tabs @click="tabsBtn">
      <van-tab :name="1" title="报工" />
      <van-tab :name="2" title="报工记录" />
    </van-tabs>

    <view v-if="activeName == 1" class="tabs-item">
      <van-button type="success" style="margin: 20rpx;" @click="detailBtn">工单详情</van-button>

      <van-button type="primary" style="margin-left: 30rpx;" @click="enterBtn"> 报工 </van-button>

      <uni-forms ref="submitFormRef" :modelValue="submitForm" label-width="80px" label-align="right"
        style="padding: 10rpx;">
        <uni-forms-item label="装箱数量:" label-width="80px" :rules="[{ required: true, errorMessage: '请输入' }]" required
          name="inNum">
          <uni-number-box :step="1" :min="1" background="#ffffff" :max="100000"
            v-model="submitForm.inNum"></uni-number-box>
        </uni-forms-item>
        <uni-forms-item label="产品条码:" label-width="80px" name="scanSn">
          <uni-easyinput v-model="submitForm.scanSn" :focus="focusProp" ref="scanCodeRef" placeholder="请扫描"
            @confirm="addCodeBtn" />
        </uni-forms-item>
      </uni-forms>
      <uni-row class="form-row" v-for="item, index in scanList" :key="item">
        <uni-col :span="20">
          {{ item }}
        </uni-col>
        <uni-col :span="4">
          <span @click="deleteCodeBtn(index)" style="color: red;">删除</span>
        </uni-col>
      </uni-row>

    </view>

    <view v-if="activeName == 2">
      <ReportRecord></ReportRecord>
    </view>

    <uniTabBar></uniTabBar>

    <OneDialog ref="dialogRef"></OneDialog>
  </view>
</template>

<script setup>
import {
  ref, onMounted, nextTick
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import OneDialog from '../modal/OneDialog.vue'
import ReportRecord from './ReportRecord.vue'
import { packReportApi } from '@/request/sys/productApi.js'

onMounted(() => { })

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/pdaproduct/index'
  });
}

//tab栏
let activeName = ref(1)
let tabsBtn = (name) => {
  activeName.value = name
}

//提交
let submitFormRef = ref(null)
let submitForm = ref({
  inNum: 1,
  scanSn: ""
})

const enterBtn = async () => {
  let flag = await submitFormRef.value.validate()
  if (flag) {
    await packReportApi({
      ...useHomeStore().productObj,
      sns: scanList.value,
      qty: submitForm.value.inNum
    })
    uni.showToast({
      title: '操作成功',
    });
    scanList.value = []
    submitForm.value = {
      inNum: 1,
      scanSn: ""
    }
  }
}


//详情
const dialogRef = ref(null)
const detailBtn = () => {
  dialogRef.value.openModal()
}

//增加删除
let focusProp = ref(true)
let scanCodeRef = ref(null)
let scanList = ref([])
const addCodeBtn = () => {
  if (!submitForm.value.scanSn.trim()) {
    return uni.showToast({
      title: '请扫码',
      icon: "none"
    })
  }
  focusProp.value = false
  if (!scanList.value.includes(submitForm.value.scanSn)) {
    scanList.value.push(submitForm.value.scanSn)
  }
  submitForm.value.scanSn = ""
  nextTick(() => {
    focusProp.value = true
  })
}
const deleteCodeBtn = (index) => {
  scanList.value.splice(index, 1)
}

</script>

<style lang="scss" scoped>
.barcode-btn {
  margin: 100rpx 0;
  width: 150rpx;
}

.form-row {
  border: 1px solid #d9d9d9;
  padding: 10rpx;
  font-size: 20rpx;
}
</style>