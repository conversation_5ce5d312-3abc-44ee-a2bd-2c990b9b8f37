<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 16:58:28
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-18 14:42:11
 * @FilePath: \bifang-pda\components\uniTabBar.vue
 * 
-->
<template>
  <view class="tab-bar">
    <view v-for="(item, index) in tabList" :key="index" class="tab-item"
      :class="{ active: currentPage === item.pagePath }" @click="switchTab(item.pagePath)">
      <text>{{ item.text }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";

// TabBar 配置（模拟 pages.json 里的 tabBar 配置）
const tabList = ref([
  { pagePath: "/pages/home/<USER>", text: "首页" },
  { pagePath: "/pages/home/<USER>", text: "我的" }
]);

// 获取当前页面路径
const currentPage = computed(() => {
  let nowRoute = '/' + getCurrentPages()[getCurrentPages().length - 1].route;
  return nowRoute == "/pages/home/<USER>" ? "/pages/home/<USER>" : "/pages/home/<USER>"
})

// 跳转到 tabBar 页面
const switchTab = (url) => {
  uni.redirectTo({ url });
};
</script>

<style lang="scss" scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  background-color: #f5f5f9;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #e5e5e7;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  color: #999999;
  padding: 10px 0;
}

.tab-item.active {
  color: #29a1f7;
  font-weight: bold;
}
</style>
