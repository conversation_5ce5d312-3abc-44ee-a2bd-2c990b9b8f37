<!--
 * @Author: 方志良 
 * @Date: 2025-03-22 13:51:40
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-08 11:59:30
 * @FilePath: \bifang-pda\pages\pdawarehouse\modal\ThreeDialog.vue
 * 
-->
<template>
  <uni-popup ref="dialogRef" type="dialog">
    <uni-popup-dialog title="添加" :before-close="true" @close="cancel" @confirm="confirm">
      <uni-forms ref='formRef' :modelValue="dialogForm" style="width: 100%;" :label-width=80>

        <uni-forms-item label="ERP工单" required name="erpOrderId" :rules="[{ required: true, errorMessage: '请选择' }]"
          style="width: 100%;">
          <uni-data-select v-model="dialogForm.erpOrderId" :localdata="erpList"></uni-data-select>
        </uni-forms-item>

        <uni-forms-item label="仓库:" name="warehouse" required :rules="[{ required: true, errorMessage: '请选择' }]">
          <uni-data-select v-model="dialogForm.warehouse" :localdata="wareList"></uni-data-select>
        </uni-forms-item>

      </uni-forms>
    </uni-popup-dialog>
  </uni-popup>
</template>

<script setup>
import { getERPorderListApi, addInWarePageApi } from '@/request/sys/warehouseApi.js'
import { getWareListApi } from '@/request/sys/sysApi.js'
import {
  ref, defineExpose,
} from 'vue';

//erp工单
let erpList = ref([])
let getERPorderListApiFn = async () => {
  let res = await getERPorderListApi({
    orderBy: 'planOnDate',
    orderDirection: 'DESC',
  })
  erpList.value = res.map(item => {
    item.value = item.id
    item.text = item.erpOrderNumber
    return item
  })
}


//仓库
let wareList = ref([])
let getWareListApiFn = async () => {
  let res = await getWareListApi({
    status: 'ENABLE',
  })
  wareList.value = res.map(item => {
    item.value = item.code
    item.text = item.name
    return item
  })
}

const dialogRef = ref(null)
const openModal = () => {
  dialogRef.value.open()
  getWareListApiFn()
  getERPorderListApiFn()
}
let dialogForm = ref({})

let formRef = ref(null)
const emit = defineEmits(['confirm']);
const confirm = () => {
  formRef.value.validate().then(async (valid) => {
    if (valid) {
      await addInWarePageApi({
        orderId: dialogForm.value.erpOrderId,
        warehouse: dialogForm.value.warehouse,
      })

      uni.showToast({ title: '操作成功' });
      emit('confirm');
      cancel()
    }
  })
}
const cancel = () => {
  dialogRef.value.close()
  dialogForm.value = {}
}

defineExpose({
  openModal
});
</script>

<style lang="scss" scoped></style>