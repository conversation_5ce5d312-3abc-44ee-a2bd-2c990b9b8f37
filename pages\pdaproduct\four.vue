<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="故障报修" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">产线信息</van-divider>

    <view class="father-row">
      <view class="son-col" style="flex: 1;"> 产线:{{ useHomeStore().lineObj.lineName }}</view>
      <view class="son-col" style="flex: 1;"> 工位:{{ useHomeStore().lineObj.stationName }}</view>
    </view>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">生产报工</van-divider>

    <van-tabs @click="tabsBtn">
      <van-tab :name="1" title="故障提报" />
      <van-tab :name="2" title="提报记录" />
    </van-tabs>

    <view v-if="activeName == 1" class="tabs-item">
      <van-button type="success" style="margin: 20rpx 0;" @click="detailBtn">工单详情</van-button>


      <uni-forms ref='submitFormRef' :modelValue="submitForm" :rules="rules" style="width: 100%;" :label-width=50>
        <uni-forms-item label="故障设备" required name="deviceId" :rules="[{ required: true, errorMessage: '请选择' }]"
          style="width: 100%;">
          <uni-data-select v-model="submitForm.deviceId" :localdata="equipList"></uni-data-select>
        </uni-forms-item>
        <uni-forms-item label="异常详情" name="exceptionDetail" required :rules="[{ required: true, errorMessage: '请输入' }]">
          <uni-easyinput type="textarea" v-model="submitForm.exceptionDetail" placeholder="请输入" />
        </uni-forms-item>
      </uni-forms>

      <view style="text-align: center;">
        <van-button type="primary" class="barcode-btn" @click="enterBtn"> 报修 </van-button>
      </view>
    </view>

    <view v-if="activeName == 2">
      <uni-search-bar @confirm="searchBtn" cancelButton="always" placeholder="输入关键词搜索" cancelText="搜索" :focus="true"
        v-model="searchValue" @cancel="searchBtn">
      </uni-search-bar>

      <uni-row class="pro-row" v-for="item in abandonCodeList" :key="item.id">
        <uni-col :span="18" class="pro-col">
          <view><span>异常单号: </span>{{ item.taskCode }}</view>
          <view><span>提报时间: </span>{{ item.reportTime }}</view>
          <view><span>异常详情: </span>{{ item.exceptionDetail }}</view>
          <view><span>处理时间: </span>{{ item.handleEndTime }}</view>
          <view><span>处理内容: </span>{{ item.handleDetail }}</view>
        </uni-col>
        <uni-col :span="6" class="pro-btn">
          <button size="mini" type="primary" v-if="item.status == 'TO_REPAIR'" @click="handleBtn(item)">开始</button>
          <button size="mini" type="primary" v-if="item.status == 'REPAIR'" @click="handleBtn(item)">继续</button>
          <button size="mini" type="primary" v-if="item.status == 'REPAIR_END'" @click="handleBtn(item)">详情</button>
        </uni-col>
      </uni-row>

    </view>

    <uniTabBar></uniTabBar>

    <OneDialog ref="dialogRef"></OneDialog>
  </view>
</template>

<script setup>
import {
  ref, onMounted
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import OneDialog from './modal/onedialog.vue'
import { addMaintainPageApi, getMaintainPageApi } from '@/request/sys/productApi.js'
import { getBaoXiuEquipListApi } from '@/request/sys/sysApi.js'

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/pdaproduct/index'
  });
}

//tab栏
let activeName = ref(1)
let tabsBtn = (name) => {
  activeName.value = name
  if (name == 2) {
    getMaintainPageApiFn()
  }
}

//设备
let equipList = ref([])

let getEquipListApiFn = async () => {
  let res = await getBaoXiuEquipListApi(useHomeStore().productObj.stationId)
  equipList.value = res.map(item => {
    item.value = item.id
    item.text = item.name
    return item
  })
}

//提交
let submitForm = ref({
})
let submitFormRef = ref(null)
const enterBtn = async () => {
  let flag = await submitFormRef.value.validate()
  if (flag) {
    await addMaintainPageApi({
      ...useHomeStore().productObj,
      ...submitForm.value
    })
    uni.showToast({
      title: '操作成功',
    });
    submitForm.value = {}
  }
}

//搜索框
let searchValue = ref("")
const searchBtn = () => {
  getMaintainPageApiFn()
}

//详情
const dialogRef = ref(null)
const detailBtn = () => {
  dialogRef.value.openModal()
}

//维修记录
let abandonCodeList = ref([])
const getMaintainPageApiFn = async () => {
  abandonCodeList.value = await getMaintainPageApi({
    orCfgs: [
      {
        name: "taskCode", type: "like:B", value: ""
      }
    ],
    orderBy: 'reportTime',
    orderDirection: 'DESC',
    pageIndex: 0,
    pageSize: 100,
    keyword: searchValue.value,
    lineId: useHomeStore().productObj.lineId,
    stationId: useHomeStore().productObj.stationId,
  })
}

//维修
const handleBtn = (row) => {
  uni.redirectTo({
    url: `/pages/pdaproduct/fourDetail?id=${row.id}&status=${row.status}`
  });
}

onMounted(() => {
  getEquipListApiFn()
})
</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.barcode-btn {
  margin: 100rpx 0;
  width: 150rpx;
}

.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }

}
</style>