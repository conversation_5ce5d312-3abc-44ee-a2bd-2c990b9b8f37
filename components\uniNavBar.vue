<!--
 * @Author: 方志良 
 * @Date: 2024-06-11 09:05:41
 * @LastEditors: 方志良 
 * @LastEditTime: 2024-11-12 10:24:42
 * @FilePath: \pda\components\uniNavBar.vue
 * 
-->
<template>
	<van-nav-bar :title="title" :left-text="leftText" :right-text="rightText" class="navbar-class" @click-right="rightclick"
		@click-left="leftclick" :left-arrow="leftArrow">
	</van-nav-bar>
</template>

<script setup>
const props = defineProps({
	title: {
		type: String,
		default: '标题'
	},
	leftText: {
		type: String,
		default: '返回'
	},
	rightText: {
		type: String,
		default: ''
	},
	leftArrow: {
		type: Boolean,
		default: true
	}
})
const emit = defineEmits()
const leftclick = () => {
	// uni.navigateBack(-1)
	emit('leftclick')
}
const rightclick = () => {
	emit('rightclick')
}
</script>

<style lang="scss" scoped>
// .navbar-class {
// 	background-color: #1b82d2;
// }

:deep(.van-nav-bar__title) {
	color: #000000;
}

:deep(.van-nav-bar__text) {
	color: #000000;
}

:deep(.van-badge__wrapper) {
	color: #000000;
}

:deep(.van-nav-bar__left span) {
	color: #1890ff;
	font-weight: 700;
}

:deep(.van-nav-bar__left i) {
	color: #1890ff;
	font-weight: 700;
}

:deep(.van-nav-bar__right span) {
	color: #1890ff;
	font-weight: 700;
}

</style>
