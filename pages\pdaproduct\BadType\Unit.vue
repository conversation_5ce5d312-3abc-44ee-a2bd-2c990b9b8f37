<!--
 * @Author: 方志良 
 * @Date: 2025-03-20 16:45:14
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-29 11:08:18
 * @FilePath: \bifang-pda\pages\pdaproduct\BadType\Unit.vue
 * 
-->
<!--
 * @Author: 方志良 
 * @Date: 2025-03-17 09:11:31
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-03-20 16:48:21
 * @FilePath: \bifang-pda\pages\pdaproduct\BadType\Unit.vue
 * 
-->
<template>
  <view class="page-content">
    <uniNavBar leftText="返回" title="不良提报-单件" @leftclick="leftclick"></uniNavBar>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">产线信息</van-divider>

    <view class="father-row">
      <view class="son-col" style="flex: 1;"> 产线:{{ useHomeStore().lineObj.lineName }}</view>
      <view class="son-col" style="flex: 1;"> 工位:{{ useHomeStore().lineObj.stationName }}</view>
    </view>

    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa' }">生产报工</van-divider>

    <van-tabs @click="tabsBtn">
      <van-tab :name="1" title="不良提报" />
      <van-tab :name="2" title="提报记录" />
    </van-tabs>

    <view v-if="activeName == 1" class="tabs-item">
      <van-button type="success" style="margin: 20rpx 0;" @click="detailBtn">工单详情</van-button>

      <uni-forms-item label="产品条码:" label-width="80px" :rules="[{ required: true, errorMessage: '请输入' }]" required
        name="barcode">
        <uni-easyinput v-model="submitForm.barcode" ref="focusRef" placeholder="请扫描" @confirm="enterInput" />
      </uni-forms-item>

      <uni-forms-item label="不良描述:" label-width="80px" name="remark">
        <uni-easyinput type="textarea" v-model="submitForm.remark" ref="focus2Ref" placeholder="请输入" />
      </uni-forms-item>

      <view style="text-align: center;">
        <van-button type="primary" class="barcode-btn" @click="enterBtn"> 报工 </van-button>
      </view>
    </view>

    <view v-if="activeName == 2">
      <ThreeRecord></ThreeRecord>
    </view>

    <uniTabBar></uniTabBar>

    <OneDialog ref="dialogRef"></OneDialog>
  </view>
</template>

<script setup>
import {
  ref, onMounted
} from 'vue';
import { useHomeStore } from '@/store/home.js'
import OneDialog from '../modal/OneDialog.vue'
import ThreeRecord from '../Record/ThreeRecord.vue'
import { badReportApi } from '@/request/sys/productApi.js'

onMounted(() => { })

const leftclick = () => {
  uni.redirectTo({
    url: '/pages/pdaproduct/index'
  });
}

//enter
let focusRef = ref(null)
let focus2Ref = ref(null)
let blurFn = (blurRef) => {
  if (blurRef) {
    const inputEl = blurRef.$el.querySelector('input, textarea');
    if (inputEl) {
      inputEl.blur();
    }
  }
}
let focusFn = (focusRef) => {
  setTimeout(() => {
    if (focusRef) {
      const inputEl = focusRef.$el.querySelector('input, textarea');
      if (inputEl) {
        inputEl.focus();
      }
    }
  }, 100);
}
const enterInput = () => {
  blurFn(focusRef.value)
  focusFn(focus2Ref.value)
}

//tab栏
let activeName = ref(1)
let tabsBtn = (name) => {
  activeName.value = name
}

let submitForm = ref({
  barcode: ""
})
let isScanning = false
let scanTimer
const enterBtn = async () => {
  if (!submitForm.value.barcode.trim()) {
    return uni.showToast({
      title: '请扫码',
      icon: "none"
    })
  }
  if (isScanning) return
  isScanning = true;
  clearTimeout(scanTimer);
  scanTimer = setTimeout(() => {
    isScanning = false;
  }, 500);

  await badReportApi({
    ...useHomeStore().productObj,
    sn: submitForm.value.barcode,
    badnessDescription: submitForm.value.remark
  })
  uni.showToast({
    title: '操作成功',
  });
  submitForm.value = {
    barcode: ""
  }
  blurFn(focus2Ref.value)
  focusFn(focusRef.value)
}


//详情
const dialogRef = ref(null)
const detailBtn = () => {
  dialogRef.value.openModal()
}

</script>

<style lang="scss" scoped>
.tabs-item {
  padding: 10rpx;
}

.barcode-btn {
  margin: 100rpx 0;
  width: 150rpx;
}

.pro-row {
  border: 1px solid #d9d9d9;
  padding: 20rpx;
  min-height: 180rpx;
  overflow: hidden;

  .pro-col {
    view {
      margin-bottom: 12rpx;
    }

    span {
      color: #666666;
    }
  }

  .pro-btn {
    padding-top: 80rpx;
    text-align: center;
  }

}
</style>