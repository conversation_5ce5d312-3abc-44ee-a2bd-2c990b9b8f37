//页面样式
page {
  background-color: #ffffff;
}

// 按钮样式
.flex-view {
  display: flex;
  justify-content: center;

  .button {
    width: 90%;
    font-size: 40rpx;
  }

  .pos-bottom {
    position: fixed;
    bottom: 0;
  }
}


.h5 {
  margin: 15rpx;
}


.all-row {
  height: calc(100vh - 520rpx);
  overflow-y: auto;

  .row-flex {
    background-color: #ffffff;
    padding: 20rpx;
    font-size: 25rpx;
    margin-bottom: 10rpx;

    .col-flex {
      margin-bottom: 10rpx;
    }
  }
}

.uni-numbox {
  border: 1px solid #e5e5e5;
  height: 35px;
  border-radius: 4px;
}

.uni-numbox__value {
  width: calc(100% - 48px) !important;
  height: 35px !important;
}

.uni-stat__select {
  background-color: #ffffff;
}


.row-style {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 10rpx;

  .van-col {
    font-size: 26rpx;
    margin-bottom: 10rpx;
    margin-left: 20rpx;
  }
}

// uni-button {
//   margin-bottom: 80rpx;
//   padding-top: 10rpx;
//   padding-bottom: 10rpx;
// }

.father-row {
  display: flex;
  width: 100%;
  flex-wrap: wrap;


  .son-col {
    margin: 15rpx 15rpx;

    button {
      background-color: #07c160;
      width: 150rpx;
      color: #ffffff;
    }
  }
}